import requests
import time
import random 
import pandas as pd

# 1. 定义目标URL和请求头
url = "https://app.miit-eidc.org.cn/miitxxgk/gonggao/xxgk/doCpQuery"
headers = {
    "Accept": "application/json, text/javascript, */*",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded",  # 表单数据格式
    "Cookie": "__snaker__id=Jx2Tkz1vPBqyPf9n; JSESSIONID=2C869A7EC49544A24250AE8DCAA03780; wzws_sessionid=oGiSGIKCMTJlZmNigWNlNjliOIAxMTcuMTQ0LjY5LjEyMQ==; gdxidpyhxdE=TBxaQSp9mjhNqUMcld%2BnffJNIEuJsld8%2Bk2w78qbAy%5CGQVND%2B1B%5CCB3KmBSxV9b2bTUCGPq38jrYltX%2FLGZGJ7BQBS7rbD2W003RHvo0brYHYJZ70yn7bUvgwwfdcv47dfaJENPHJcxCUXqPIMQbOxVElppzZuqSnxrLD%5CL57gIvRTZ8%3A1754411190636",  # 替换为完整Cookie
    "Host": "app.miit-eidc.org.cn",
    "Origin": "https://app.miit-eidc.org.cn",
    "Referer": "https://app.miit-eidc.org.cn/miitxxgk/gonggao_xxgk/index_ggcp.html",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin"
}

# 2. 构造请求体（示例参数，需根据实际需求修改）
pagenum = 1
chunduan_payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E7%25BA%25AF%25E7%2594%25B5&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={pagenum}'
chadian_payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E6%258F%2592%25E7%2594%25B5&scdz=&cplb=0&cxtype=&pageSize=10&pageNum=1'



def get_data(veh_type):


    final_data_list = []
    # 3. 发送POST请求
    try:

    # https://app.miit-eidc.org.cn/miitxxgk/gonggao/xxgk/queryCpData?dataTag=Z&gid=Y7123907&pc=347
        
        current_page = 1

        is_continue = True
        while is_continue:

            if veh_type == '纯电':

                payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E7%25BA%25AF%25E7%2594%25B5&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={current_page}'
            
            else:
                payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E6%258F%2592%25E7%2594%25B5&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={current_page}'


            # first_payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E7%25BA%25AF%25E7%2594%25B5&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={current_page}'
            response = requests.post(
                url,
                data=payload,  # 使用 data 参数发送表单数据
                headers=headers,
                timeout=10  # 超时设置
            )
            response.raise_for_status()  # 检查HTTP错误


            # 4. 处理响应（假设返回JSON）
            if response.status_code == 200:
                result = response.json()  # 解析JSON响应
                total_page = result['countResult']['totalPage']
                current_page_get = result['countResult']['pageNum']

                print(f'当前页: {current_page_get}, 总页数: {total_page}---------------------------------')

                if current_page_get == total_page:
                    is_continue = False
                

                data_list = result['cpList']
                for item in data_list:
                    clmc = item['clmc']
                    if '底盘' in clmc:
                        continue
                    else:
                        if '轿车' in clmc or '乘用车' in clmc:
                            final_data_list.append([item['clmc'], item['cpsb'], item['clxh'], item['qymc'], item['dataTag'], item['gid'], item['pc']])
                            print(final_data_list[-1])

                        if len(final_data_list) > 1:
                            df = pd.DataFrame(final_data_list, columns=['车辆名称', '中文品牌', '车辆型号', '公司名称', 'tag', 'gid', 'pc'])
                            df.to_csv(f'./veh/infos_{veh_type}_{current_page}.csv', index=False)

                # # 车辆名称
                # clmc_list = [item['clmc'] for item in data_list]
                # # 中文品牌
                # cpsb_list = [item['cpsb'] for item in data_list]
                # # 车辆型号
                # clxh_list = [item['clxh'] for item in data_list]
                # # 公司名称
                # qymc_list = [item['qymc'] for item in data_list]


            else:
                print(f"请求失败！状态码: {response.status_code}, 响应文本: {response.text}")

            

            current_page += 1
            sleep_time = random.randint(5, 12)
            time.sleep(sleep_time)

    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")


get_data('纯电')

# get_data('插电')
