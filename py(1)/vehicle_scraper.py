#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车辆信息爬虫 - 成功版本
基于真实浏览器请求头和AJAX接口
目标: 成功获取车辆信息数据
"""

import requests
import json
import time
import random
from urllib.parse import urljoin


class SuccessfulVehicleScraper:
    def __init__(self):
        self.session = requests.Session()
        self.setup_real_session()
        
    def setup_real_session(self):
        """使用真实浏览器的请求头设置会话"""
        # 基于用户提供的真实请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'X-Requested-With': 'XMLHttpRequest',
            'Priority': 'u=1, i'
        })
        
    def get_verification_token(self, base_url="http://www.jdcsww.com"):
        """获取验证令牌"""
        try:
            print("正在获取验证令牌...")
            
            # 首先访问主页面获取token
            main_url = f"{base_url}/qcggdetail?bh=SV2025072804092"
            response = self.session.get(main_url, timeout=15)
            
            if response.status_code == 200:
                # 从响应中提取token（通常在cookie或页面中）
                cookies = response.cookies
                for cookie in cookies:
                    if 'RequestVerificationToken' in cookie.name:
                        print(f"找到验证令牌: {cookie.value[:20]}...")
                        return cookie.value
                
                # 如果cookie中没有，尝试从页面内容中提取
                if '__RequestVerificationToken' in response.text:
                    import re
                    token_match = re.search(r'__RequestVerificationToken["\']?\s*:\s*["\']([^"\']+)', response.text)
                    if token_match:
                        token = token_match.group(1)
                        print(f"从页面提取到令牌: {token[:20]}...")
                        return token
                
                print("使用默认令牌...")
                return "WEz6pBAF84GPJaHkxYBhOqI0v73HCET7yu9fdF1JM_chSoKiV7Uj7XN9q7zROILH3MEXdoO6OaRAJO8Ibagtan96fPXiofDJhMWNmjOOJew1"
            
        except Exception as e:
            print(f"获取令牌失败: {e}")
            return "WEz6pBAF84GPJaHkxYBhOqI0v73HCET7yu9fdF1JM_chSoKiV7Uj7XN9q7zROILH3MEXdoO6OaRAJO8Ibagtan96fPXiofDJhMWNmjOOJew1"
    
    def get_vehicle_images(self, bh="SV2025072804092", clggbh="", base_url="http://www.jdcsww.com"):
        """获取车辆图片信息（这可能包含车辆数据）"""
        try:
            print(f"正在获取车辆图片信息: {bh}")
            
            # 设置请求头
            token = self.get_verification_token(base_url)
            
            # 设置cookie
            self.session.cookies.set('__RequestVerificationToken', token)
            self.session.cookies.set('addcontentvisit', '0')
            
            # 设置referer
            self.session.headers['Referer'] = f'{base_url}/qcggdetail?bh={bh}'
            
            # 构建API请求URL
            api_url = f"{base_url}/getimgs?bh={bh}&clggbh={clggbh}"
            
            print(f"请求URL: {api_url}")
            
            # 发送请求
            response = self.session.get(api_url, timeout=15)
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print("✓ 成功获取JSON数据!")
                    return data
                except json.JSONDecodeError:
                    print("响应不是JSON格式，返回文本内容")
                    return {"text_content": response.text}
            else:
                print(f"请求失败，状态码: {response.status_code}")
                return {"error": f"HTTP {response.status_code}", "content": response.text}
                
        except Exception as e:
            print(f"获取车辆图片信息失败: {e}")
            return {"error": str(e)}
    
    def get_vehicle_detail(self, bh="SV2025072804092", base_url="http://www.jdcsww.com"):
        """获取车辆详细信息"""
        try:
            print(f"正在获取车辆详细信息: {bh}")
            
            # 可能的其他API端点
            api_endpoints = [
                f"{base_url}/getdetail?bh={bh}",
                f"{base_url}/api/vehicle?bh={bh}",
                f"{base_url}/qcggdetail/getdata?bh={bh}",
                f"{base_url}/vehicle/detail?bh={bh}"
            ]
            
            for api_url in api_endpoints:
                try:
                    print(f"尝试API: {api_url}")
                    response = self.session.get(api_url, timeout=10)
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            print(f"✓ API成功: {api_url}")
                            return data
                        except:
                            if len(response.text) > 100:  # 有实际内容
                                print(f"✓ 获取到文本数据: {api_url}")
                                return {"text_content": response.text}
                    
                except Exception as e:
                    print(f"API失败 {api_url}: {e}")
                    continue
            
            return None
            
        except Exception as e:
            print(f"获取车辆详细信息失败: {e}")
            return {"error": str(e)}
    
    def scrape_vehicle_info(self, bh="SV2025072804092", base_url="http://www.jdcsww.com"):
        """主要爬取方法"""
        print("=" * 60)
        print("车辆信息爬虫 - 成功版本")
        print("=" * 60)
        print(f"目标车辆编号: {bh}")
        print(f"目标URL: {base_url}/qcggdetail?bh={bh}")
        print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {
            "vehicle_id": bh,
            "base_url": base_url,
            "scrape_time": time.strftime('%Y-%m-%d %H:%M:%S'),
            "status": "success",
            "data": {}
        }
        
        # 方法1: 获取图片信息
        print("\n--- 方法1: 获取车辆图片信息 ---")
        images_data = self.get_vehicle_images(bh, "", base_url)
        if images_data and not images_data.get("error"):
            results["data"]["images"] = images_data
            print("✓ 图片信息获取成功")
        else:
            print("✗ 图片信息获取失败")
        
        # 方法2: 尝试其他API端点
        print("\n--- 方法2: 尝试其他API端点 ---")
        detail_data = self.get_vehicle_detail(bh, base_url)
        if detail_data and not detail_data.get("error"):
            results["data"]["details"] = detail_data
            print("✓ 详细信息获取成功")
        else:
            print("✗ 详细信息获取失败")
        
        # 方法3: 直接访问页面并解析
        print("\n--- 方法3: 直接页面访问 ---")
        page_data = self.get_page_content(bh, base_url)
        if page_data:
            results["data"]["page_content"] = page_data
            print("✓ 页面内容获取成功")
        else:
            print("✗ 页面内容获取失败")
        
        return results
    
    def get_page_content(self, bh, base_url="http://www.jdcsww.com"):
        """获取页面内容"""
        try:
            url = f"{base_url}/qcggdetail?bh={bh}"
            
            # 更新请求头为页面请求
            page_headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            }
            
            # 临时更新请求头
            original_headers = self.session.headers.copy()
            self.session.headers.update(page_headers)
            
            response = self.session.get(url, timeout=15)
            
            # 恢复原始请求头
            self.session.headers = original_headers
            
            if response.status_code == 200:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 提取有用信息
                page_info = {
                    "title": soup.find('title').get_text() if soup.find('title') else "",
                    "tables": [],
                    "scripts": [],
                    "forms": []
                }
                
                # 提取表格
                tables = soup.find_all('table')
                for table in tables:
                    rows = []
                    for row in table.find_all('tr'):
                        cells = [cell.get_text().strip() for cell in row.find_all(['td', 'th'])]
                        if any(cells):
                            rows.append(cells)
                    if rows:
                        page_info["tables"].append(rows)
                
                # 提取脚本中的数据
                scripts = soup.find_all('script')
                for script in scripts:
                    script_text = script.get_text()
                    if 'vehicle' in script_text.lower() or 'data' in script_text.lower():
                        page_info["scripts"].append(script_text[:500])  # 只保存前500字符
                
                return page_info
            
            return None
            
        except Exception as e:
            print(f"获取页面内容失败: {e}")
            return None
    
    def save_results(self, data, vehicle_id=None):
        """保存结果到分类文件夹"""
        import os
        
        try:
            # 获取车辆编号
            if not vehicle_id:
                vehicle_id = data.get('vehicle_id', 'unknown')
            
            # 创建文件夹
            json_dir = 'json'
            csv_dir = 'csv'
            os.makedirs(json_dir, exist_ok=True)
            os.makedirs(csv_dir, exist_ok=True)
            
            # 生成文件名
            json_filename = os.path.join(json_dir, f"vehicle_{vehicle_id}.json")
            
            # 保存JSON文件
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"\n✓ JSON文件已保存到: {json_filename}")
            
            # 自动生成CSV文件
            self.auto_generate_csv(data, vehicle_id, csv_dir)
            
            return True
        except Exception as e:
            print(f"\n✗ 保存失败: {e}")
            return False
    
    def auto_generate_csv(self, data, vehicle_id, csv_dir):
        """自动生成CSV文件到指定文件夹"""
        try:
            from json_to_csv import extract_key_vehicle_info
            import pandas as pd
            import os
            
            # 提取关键信息
            vehicle_info = extract_key_vehicle_info(data)
            
            # 生成CSV文件名
            csv_filename = os.path.join(csv_dir, f"vehicle_{vehicle_id}.csv")
            
            # 创建DataFrame并保存
            df = pd.DataFrame([vehicle_info])
            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            
            print(f"✓ CSV文件已保存到: {csv_filename}")
            print(f"  包含 {len(vehicle_info)} 个关键字段")
            
        except ImportError:
            print("⚠️ 无法导入CSV转换模块，请手动运行 python json_to_csv.py")
        except Exception as e:
            print(f"⚠️ CSV自动生成失败: {e}")


def scrape_from_url(url):
    """从完整URL爬取车辆信息的便捷函数"""
    import re
    from urllib.parse import urlparse, parse_qs
    
    # 解析URL
    parsed = urlparse(url)
    base_url = f"{parsed.scheme}://{parsed.netloc}"
    
    # 提取车辆编号
    query_params = parse_qs(parsed.query)
    bh = query_params.get('bh', [''])[0]
    
    if not bh:
        print("❌ 无法从URL中提取车辆编号(bh参数)")
        return None
    
    print(f"📋 解析URL:")
    print(f"   基础URL: {base_url}")
    print(f"   车辆编号: {bh}")
    print(f"   完整URL: {url}")
    
    # 创建爬虫并执行
    scraper = SuccessfulVehicleScraper()
    result = scraper.scrape_vehicle_info(bh, base_url)
    
    # 保存结果
    if result and result.get('status') == 'success':
        scraper.save_results(result, bh)
    
    return result


def main():
    """主函数"""
    # 示例1: 直接使用URL爬取
    # 可以修改这里的车辆编号来爬取不同的车辆信息
    target_url = "http://www.jdcsww.com/qcggdetail?bh=SV2025072804092"
    print("🚀 使用URL直接爬取:")
    print(f"📝 提示: 可以修改代码中的车辆编号来爬取不同车辆")
    result = scrape_from_url(target_url)
    
    # 示例2: 传统方式爬取
    # scraper = SuccessfulVehicleScraper()
    # result = scraper.scrape_vehicle_info("SV2025072804092", "http://www.jdcsww.com")
    
    if result:
        # 显示结果
        print("\n" + "=" * 60)
        print("爬取结果摘要")
        print("=" * 60)
        
        if result["data"]:
            for key, value in result["data"].items():
                if isinstance(value, dict):
                    print(f"{key}: {len(value)} 个字段")
                elif isinstance(value, list):
                    print(f"{key}: {len(value)} 个项目")
                else:
                    print(f"{key}: 已获取")
            
            # 保存结果
            scraper = SuccessfulVehicleScraper()
            scraper.save_results(result, result.get('vehicle_id'))
            print("\n🎉 爬取任务完成!")
            
            # 显示部分数据预览
            print("\n--- 数据预览 ---")
            for key, value in result["data"].items():
                print(f"\n{key}:")
                if isinstance(value, dict):
                    for k, v in list(value.items())[:3]:  # 只显示前3个
                        print(f"  {k}: {str(v)[:100]}...")
                elif isinstance(value, list):
                    for i, item in enumerate(value[:2]):  # 只显示前2个
                        print(f"  [{i}]: {str(item)[:100]}...")
        else:
            print("未获取到有效数据")
            print("可能的原因:")
            print("1. 需要登录认证")
            print("2. API端点已变更")
            print("3. 需要额外的参数")
    else:
        print("❌ 爬取失败")


if __name__ == "__main__":
    main()
