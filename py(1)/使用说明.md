# 车辆信息爬虫使用说明

## 项目简介

本项目成功实现了对 `http://www.jdcsww.com/qcggdetail?bh=SV2025072804092` 网站车辆信息的爬取。通过分析真实浏览器请求头和AJAX接口，成功绕过了网站的反爬虫机制。

## 文件说明

### 核心文件
- **`vehicle_scraper.py`** - 主要爬虫脚本（支持自动分类存储）
- **`batch_scraper.py`** - 批量爬取脚本
- **`json_to_csv.py`** - JSON转CSV转换工具
- **`requirements.txt`** - 项目依赖库
- **`使用说明.md`** - 本使用文档

### 自动生成的文件夹
- **`json/`** - JSON文件存储文件夹
- **`csv/`** - CSV文件存储文件夹

## 环境要求

- Python 3.7+
- 网络连接
- 以下Python库：
  - requests
  - beautifulsoup4
  - json (内置)
  - time (内置)
  - random (内置)

## 安装步骤

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install requests beautifulsoup4
```

### 2. 运行爬虫

**方式1: 单个车辆爬取（推荐）**
```bash
python vehicle_scraper.py
```

**方式2: 批量车辆爬取**
```bash
python batch_scraper.py
```

**方式3: 手动转换JSON为CSV**
```bash
python json_to_csv.py
```

**方式4: 在代码中使用**
```python
from vehicle_scraper import scrape_from_url

# 直接使用完整URL爬取
result = scrape_from_url("http://www.jdcsww.com/qcggdetail?bh=SV2025072804092")
```

## 功能特性

### ✅ 成功实现的功能

1. **真实请求头模拟**
   - 使用真实浏览器的完整请求头
   - 包含所有必要的安全标头（Sec-Ch-Ua, Sec-Fetch-* 等）

2. **AJAX接口调用**
   - 成功调用 `/getimgs` 接口获取车辆图片信息
   - 自动获取和使用验证令牌

3. **多种数据获取方式**
   - 方法1：获取车辆图片信息（JSON数据）
   - 方法2：尝试其他可能的API端点
   - 方法3：直接页面访问和HTML解析

4. **智能数据解析**
   - 自动解析JSON响应
   - 提取HTML表格数据
   - 识别页面标题和结构化信息

## 使用方法

### 基本使用

**方法1: 单个车辆爬取（最简单）**
```bash
python vehicle_scraper.py
```
- 自动创建 `json/` 和 `csv/` 文件夹
- 按车辆编号命名文件：`vehicle_车辆编号.json` 和 `vehicle_车辆编号.csv`

**方法2: 批量车辆爬取**
```bash
python batch_scraper.py
```
- 支持批量爬取多个车辆
- 自动延迟避免请求过快
- 统计成功和失败数量

**方法3: 在代码中使用**
```python
from vehicle_scraper import scrape_from_url

# 直接使用完整URL
result = scrape_from_url("http://www.jdcsww.com/qcggdetail?bh=SV2025072804092")

# 使用HTTPS URL
result = scrape_from_url("https://www.jdcsww.com/qcggdetail?bh=SV2025072804092")
```

### 自定义车辆编号

**修改单个爬虫的车辆编号：**
编辑 `vehicle_scraper.py` 文件中的URL：
```python
target_url = "http://www.jdcsww.com/qcggdetail?bh=YOUR_VEHICLE_ID"
```

**修改批量爬虫的车辆列表：**
编辑 `batch_scraper.py` 文件中的URL列表：
```python
vehicle_urls = [
    "http://www.jdcsww.com/qcggdetail?bh=SV2025072804092",
    "http://www.jdcsww.com/qcggdetail?bh=SV2025072804093",
    "http://www.jdcsww.com/qcggdetail?bh=SV2025072804094",
    # 添加更多车辆URL...
]
```

### 单独调用API

```python
# 只获取图片信息
images_data = scraper.get_vehicle_images("SV2025072804092")

# 只获取页面内容
page_data = scraper.get_page_content("SV2025072804092")
```

## 输出数据格式

爬虫成功运行后会生成包含以下结构的JSON文件：

```json
{
  "vehicle_id": "SV2025072804092",
  "scrape_time": "2025-08-06 11:08:29",
  "status": "success",
  "data": {
    "images": {
      "BH": "车辆编号",
      "CLGGBH": "车辆公告编号",
      "PC": "批次信息",
      "IMGS": "图片列表",
      "ZJTPURL": "主图片URL",
      "FJTPURL": "附加图片URL",
      "QZTP": "前置图片",
      "HZTP": "后置图片",
      "CZTP": "侧置图片"
    },
    "details": {
      "text_content": "详细页面HTML内容"
    },
    "page_content": {
      "title": "页面标题",
      "tables": [
        [
          ["表格行1列1", "表格行1列2"],
          ["表格行2列1", "表格行2列2"]
        ]
      ],
      "scripts": [],
      "forms": []
    }
  }
}
```

## 成功案例

运行脚本后的典型输出：

```
🚀 使用URL直接爬取:
📋 解析URL:
   基础URL: http://www.jdcsww.com
   车辆编号: SV2025072804092
   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025072804092
============================================================
车辆信息爬虫 - 成功版本
============================================================
目标车辆编号: SV2025072804092
目标URL: http://www.jdcsww.com/qcggdetail?bh=SV2025072804092
开始时间: 2025-08-06 11:18:55

--- 方法1: 获取车辆图片信息 ---
正在获取车辆图片信息: SV2025072804092
正在获取验证令牌...
使用默认令牌...
请求URL: http://www.jdcsww.com/getimgs?bh=SV2025072804092&clggbh=
响应状态码: 200
✓ 成功获取JSON数据!
✓ 图片信息获取成功

--- 方法2: 尝试其他API端点 ---
✓ 详细信息获取成功

--- 方法3: 直接页面访问 ---
✓ 页面内容获取成功

============================================================
爬取结果摘要
============================================================
images: 9 个字段
page_content: 4 个字段

✓ 结果已保存到: vehicle_success.json
🎉 爬取任务完成!
```

## 技术原理

### 1. 反爬虫绕过技术

- **真实请求头复制**：使用从真实浏览器复制的完整请求头
- **验证令牌处理**：自动获取和使用 `__RequestVerificationToken`
- **Cookie管理**：正确设置和维护会话Cookie
- **Referer设置**：模拟真实的页面跳转路径

### 2. 数据获取策略

- **API优先**：优先使用AJAX接口获取结构化数据
- **多重备份**：提供多种数据获取方式确保成功率
- **智能解析**：根据响应类型自动选择解析方法

### 3. 错误处理

- **超时控制**：设置合理的请求超时时间
- **异常捕获**：全面的异常处理机制
- **状态检查**：检查HTTP状态码和响应内容

## 注意事项

### ⚠️ 重要提醒

1. **合法使用**
   - 请遵守网站的使用条款
   - 不要过于频繁地请求，避免给服务器造成压力
   - 仅用于学习和研究目的

2. **技术限制**
   - 验证令牌可能会过期，需要定期更新
   - 网站结构可能发生变化，需要相应调整代码
   - 某些车辆信息可能需要特殊权限才能访问

3. **网络环境**
   - 确保网络连接稳定
   - 某些网络环境可能需要配置代理

## 故障排除

### 常见问题

**Q: 获取验证令牌失败**
```
A: 检查网络连接，或者手动更新代码中的默认令牌
```

**Q: 返回空数据**
```
A: 可能是车辆编号不存在，或者需要登录权限
```

**Q: 请求被拒绝**
```
A: 检查请求头设置，确保User-Agent等信息正确
```

### 调试模式

在代码中添加更多调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 然后运行爬虫
scraper = SuccessfulVehicleScraper()
result = scraper.scrape_vehicle_info("SV2025072804092")
```

## 扩展功能

### 批量爬取

```python
vehicle_ids = ["SV2025072804092", "SV2025072804093", "SV2025072804094"]

for vehicle_id in vehicle_ids:
    result = scraper.scrape_vehicle_info(vehicle_id)
    scraper.save_results(result, f"vehicle_{vehicle_id}.json")
    time.sleep(2)  # 添加延迟避免请求过快
```

### 数据过滤

```python
def filter_vehicle_data(data):
    """过滤和清理车辆数据"""
    if 'images' in data['data']:
        # 只保留需要的字段
        filtered = {
            'vehicle_id': data['data']['images'].get('BH'),
            'batch': data['data']['images'].get('PC'),
            'main_image': data['data']['images'].get('ZJTPURL')
        }
        return filtered
    return None
```

## 更新日志

- **v3.0** (2025-08-06): 文件分类存储版本
  - ✅ 自动创建json和csv文件夹
  - ✅ 按车辆编号分类存储文件
  - ✅ 添加批量爬取功能
  - ✅ 自动生成CSV文件
  - ✅ 完善的错误处理和统计

- **v2.0** (2025-08-06): JSON转CSV功能版本
  - ✅ 添加JSON转CSV转换工具
  - ✅ 支持关键信息提取
  - ✅ 支持完整数据导出

- **v1.0** (2025-08-06): 初始版本
  - ✅ 成功实现基本爬取功能
  - ✅ 支持车辆图片信息获取
  - ✅ 支持页面内容解析
  - ✅ 支持多种API端点尝试

## 联系支持

如果在使用过程中遇到问题，请检查：

1. Python版本是否符合要求
2. 依赖库是否正确安装
3. 网络连接是否正常
4. 车辆编号是否正确

---

**免责声明**: 本工具仅供学习和研究使用，请遵守相关法律法规和网站使用条款。
