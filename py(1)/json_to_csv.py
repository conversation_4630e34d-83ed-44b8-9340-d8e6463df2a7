#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON转CSV工具
将车辆信息JSON文件转换为CSV格式
"""

import json
import csv
import pandas as pd
from datetime import datetime
import os


def flatten_json(data, parent_key='', sep='_'):
    """
    将嵌套的JSON数据扁平化
    """
    items = []
    if isinstance(data, dict):
        for k, v in data.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(flatten_json(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                # 处理列表数据
                if v and isinstance(v[0], list):
                    # 处理表格数据
                    for i, row in enumerate(v):
                        if isinstance(row, list):
                            for j, cell in enumerate(row):
                                items.append((f"{new_key}_table_{i}_col_{j}", cell))
                        else:
                            items.append((f"{new_key}_{i}", row))
                else:
                    for i, item in enumerate(v):
                        items.append((f"{new_key}_{i}", item))
            else:
                items.append((new_key, v))
    elif isinstance(data, list):
        for i, item in enumerate(data):
            items.extend(flatten_json(item, f"{parent_key}_{i}", sep=sep).items())
    else:
        items.append((parent_key, data))
    
    return dict(items)


def extract_key_vehicle_info(json_data):
    """
    提取关键车辆信息到结构化格式
    """
    vehicle_info = {}
    
    # 基本信息
    vehicle_info['车辆编号'] = json_data.get('vehicle_id', '')
    vehicle_info['爬取时间'] = json_data.get('scrape_time', '')
    vehicle_info['基础URL'] = json_data.get('base_url', '')
    vehicle_info['状态'] = json_data.get('status', '')
    
    # 图片信息
    if 'images' in json_data.get('data', {}):
        images = json_data['data']['images']
        vehicle_info['公告批次'] = images.get('PC', '')
        vehicle_info['车辆公告编号'] = images.get('CLGGBH', '')
        vehicle_info['图片1'] = images.get('IMG1', '')
        vehicle_info['图片2'] = images.get('IMG2', '')
        vehicle_info['图片3'] = images.get('IMG3', '')
    
    # 页面内容信息
    if 'page_content' in json_data.get('data', {}):
        page_content = json_data['data']['page_content']
        vehicle_info['车辆标题'] = page_content.get('title', '')
        
        # 解析表格数据
        tables = page_content.get('tables', [])
        if len(tables) > 1:  # 第二个表格通常包含详细信息
            main_table = tables[1]
            
            # 提取关键信息
            for row in main_table:
                if len(row) >= 2:
                    if '车辆名称：' in row:
                        idx = row.index('车辆名称：')
                        if idx + 1 < len(row):
                            vehicle_info['车辆名称'] = row[idx + 1]
                    
                    if '公告批次：' in row:
                        idx = row.index('公告批次：')
                        if idx + 1 < len(row):
                            vehicle_info['公告批次'] = row[idx + 1]
                    
                    if '发布日期：' in row:
                        idx = row.index('发布日期：')
                        if idx + 1 < len(row):
                            vehicle_info['发布日期'] = row[idx + 1]
                    
                    if '产品号：' in row:
                        idx = row.index('产品号：')
                        if idx + 1 < len(row):
                            vehicle_info['产品号'] = row[idx + 1]
                    
                    if '中文品牌：' in row:
                        idx = row.index('中文品牌：')
                        if idx + 1 < len(row):
                            vehicle_info['中文品牌'] = row[idx + 1]
                    
                    if '公告型号：' in row:
                        idx = row.index('公告型号：')
                        if idx + 1 < len(row):
                            vehicle_info['公告型号'] = row[idx + 1]
                    
                    if '企业名称：' in row:
                        idx = row.index('企业名称：')
                        if idx + 1 < len(row):
                            vehicle_info['企业名称'] = row[idx + 1]
                    
                    if '产品ID号：' in row:
                        idx = row.index('产品ID号：')
                        if idx + 1 < len(row):
                            vehicle_info['产品ID号'] = row[idx + 1]
                    
                    if '车辆总质量(Kg)：' in row:
                        idx = row.index('车辆总质量(Kg)：')
                        if idx + 1 < len(row):
                            vehicle_info['车辆总质量(Kg)'] = row[idx + 1]
                    
                    if '车辆整备质量(Kg)：' in row:
                        idx = row.index('车辆整备质量(Kg)：')
                        if idx + 1 < len(row):
                            vehicle_info['车辆整备质量(Kg)'] = row[idx + 1]
                    
                    if '最高车速(Km/h)：' in row:
                        idx = row.index('最高车速(Km/h)：')
                        if idx + 1 < len(row):
                            vehicle_info['最高车速(Km/h)'] = row[idx + 1]
                    
                    if '车辆轴距(mm)：' in row:
                        idx = row.index('车辆轴距(mm)：')
                        if idx + 1 < len(row):
                            vehicle_info['车辆轴距(mm)'] = row[idx + 1]
                    
                    if '外形尺寸长Ⅹ宽Ⅹ高(mm)：' in row:
                        idx = row.index('外形尺寸长Ⅹ宽Ⅹ高(mm)：')
                        if idx + 1 < len(row):
                            vehicle_info['外形尺寸(mm)'] = row[idx + 1]
                    
                    if '车辆轮胎规格型号：' in row:
                        idx = row.index('车辆轮胎规格型号：')
                        if idx + 1 < len(row):
                            vehicle_info['轮胎规格型号'] = row[idx + 1]
                    
                    if '公告状态：' in row:
                        idx = row.index('公告状态：')
                        if idx + 1 < len(row):
                            vehicle_info['公告状态'] = row[idx + 1]
                    
                    if '公告生效日期：' in row:
                        idx = row.index('公告生效日期：')
                        if idx + 1 < len(row):
                            vehicle_info['公告生效日期'] = row[idx + 1]
    
    return vehicle_info


def json_to_csv_simple(json_file, csv_file):
    """
    将JSON转换为简化的CSV格式（只包含关键信息）
    """
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取关键信息
        vehicle_info = extract_key_vehicle_info(data)
        
        # 创建DataFrame
        df = pd.DataFrame([vehicle_info])
        
        # 保存为CSV
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        print(f"✓ 简化CSV文件已生成: {csv_file}")
        print(f"  包含 {len(vehicle_info)} 个关键字段")
        
        return True
        
    except Exception as e:
        print(f"✗ 转换失败: {str(e)}")
        return False


def json_to_csv_full(json_file, csv_file):
    """
    将JSON转换为完整的CSV格式（包含所有数据）
    """
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 扁平化JSON数据
        flattened_data = flatten_json(data)
        
        # 创建DataFrame
        df = pd.DataFrame([flattened_data])
        
        # 保存为CSV
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        print(f"✓ 完整CSV文件已生成: {csv_file}")
        print(f"  包含 {len(flattened_data)} 个字段")
        
        return True
        
    except Exception as e:
        print(f"✗ 转换失败: {str(e)}")
        return False


def batch_convert(input_dir='.', output_dir='csv_output'):
    """
    批量转换目录中的所有JSON文件
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    json_files = [f for f in os.listdir(input_dir) if f.endswith('.json')]
    
    if not json_files:
        print("未找到JSON文件")
        return
    
    print(f"找到 {len(json_files)} 个JSON文件")
    
    for json_file in json_files:
        base_name = os.path.splitext(json_file)[0]
        
        # 生成简化版CSV
        simple_csv = os.path.join(output_dir, f"{base_name}_simple.csv")
        json_to_csv_simple(os.path.join(input_dir, json_file), simple_csv)
        
        # 生成完整版CSV
        full_csv = os.path.join(output_dir, f"{base_name}_full.csv")
        json_to_csv_full(os.path.join(input_dir, json_file), full_csv)
        
        print()


def main():
    """主函数"""
    print("=" * 60)
    print("JSON转CSV工具")
    print("=" * 60)
    
    # 检查是否存在vehicle_success.json
    if os.path.exists('vehicle_success.json'):
        print("发现 vehicle_success.json 文件")
        
        # 生成简化版CSV（推荐）
        print("\n--- 生成简化版CSV（包含关键信息） ---")
        json_to_csv_simple('vehicle_success.json', 'vehicle_info_simple.csv')
        
        # 生成完整版CSV
        print("\n--- 生成完整版CSV（包含所有数据） ---")
        json_to_csv_full('vehicle_success.json', 'vehicle_info_full.csv')
        
        print("\n🎉 转换完成!")
        print("文件说明:")
        print("- vehicle_info_simple.csv: 包含关键车辆信息，便于阅读")
        print("- vehicle_info_full.csv: 包含所有原始数据，用于完整分析")
        
    else:
        print("未找到 vehicle_success.json 文件")
        print("请先运行爬虫生成JSON数据")
        
        # 批量转换功能
        print("\n尝试批量转换当前目录中的所有JSON文件...")
        batch_convert()


if __name__ == "__main__":
    main()
