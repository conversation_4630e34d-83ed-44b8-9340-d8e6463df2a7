#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量车辆信息爬虫
用于批量爬取多个车辆的信息
"""

from vehicle_scraper import scrape_from_url
import time
import random


def batch_scrape_vehicles(vehicle_urls, delay_range=(2, 5)):
    """
    批量爬取多个车辆信息
    
    Args:
        vehicle_urls: 车辆URL列表
        delay_range: 延迟时间范围(秒)，避免请求过快
    """
    import os
    
    print("=" * 70)
    print("批量车辆信息爬虫")
    print("=" * 70)
    print(f"准备爬取 {len(vehicle_urls)} 个车辆信息")
    
    success_count = 0
    failed_count = 0
    success_vehicles = []
    failed_vehicles = []
    
    for i, url in enumerate(vehicle_urls, 1):
        print(f"\n[{i}/{len(vehicle_urls)}] 正在爬取: {url}")
        
        try:
            result = scrape_from_url(url)
            
            if result and result.get('status') == 'success':
                success_count += 1
                vehicle_id = result.get('vehicle_id', 'unknown')
                success_vehicles.append(vehicle_id)
                print(f"✓ 第 {i} 个车辆爬取成功: {vehicle_id}")
            else:
                failed_count += 1
                failed_vehicles.append(url)
                print(f"✗ 第 {i} 个车辆爬取失败: 可能车辆编号不存在")
                
        except Exception as e:
            failed_count += 1
            failed_vehicles.append(url)
            print(f"✗ 第 {i} 个车辆爬取异常: {str(e)}")
        
        # 添加随机延迟，避免请求过快
        if i < len(vehicle_urls):  # 最后一个不需要延迟
            delay = random.uniform(delay_range[0], delay_range[1])
            print(f"⏳ 等待 {delay:.1f} 秒...")
            time.sleep(delay)
    
    print("\n" + "=" * 70)
    print("批量爬取完成")
    print("=" * 70)
    print(f"✓ 成功: {success_count} 个")
    print(f"✗ 失败: {failed_count} 个")
    
    # 显示成功爬取的车辆
    if success_vehicles:
        print(f"\n📋 成功爬取的车辆:")
        for vehicle_id in success_vehicles:
            print(f"   - {vehicle_id}")
    
    # 显示失败的URL
    if failed_vehicles:
        print(f"\n❌ 失败的车辆URL:")
        for url in failed_vehicles:
            print(f"   - {url}")
    
    # 列出实际生成的文件
    print(f"\n📁 实际生成的文件:")
    
    # 检查JSON文件
    json_dir = './json'
    if os.path.exists(json_dir):
        json_files = [f for f in os.listdir(json_dir) if f.endswith('.json')]
        if json_files:
            print(f"   JSON文件 ({len(json_files)} 个):")
            for file in json_files:
                print(f"     - {file}")
        else:
            print(f"   JSON文件: 无")
    else:
        print(f"   JSON文件夹不存在")
    
    # 检查CSV文件
    csv_dir = './csv'
    if os.path.exists(csv_dir):
        csv_files = [f for f in os.listdir(csv_dir) if f.endswith('.csv')]
        if csv_files:
            print(f"   CSV文件 ({len(csv_files)} 个):")
            for file in csv_files:
                print(f"     - {file}")
        else:
            print(f"   CSV文件: 无")
    else:
        print(f"   CSV文件夹不存在")


def main():
    """主函数 - 批量爬取示例"""
    
    # 示例车辆URL列表
    # 你可以修改这里的车辆编号来爬取不同的车辆
    vehicle_urls = [
        "http://www.jdcsww.com/qcggdetail?bh=SV2025072804092",
        "http://www.jdcsww.com/qcggdetail?bh=SV2025072804096", 
        "http://www.jdcsww.com/qcggdetail?bh=SV2025072804097",
        "http://www.jdcsww.com/qcggdetail?bh=SV2025072804099",
    ]
    
    print("🚀 批量爬取车辆信息")
    print("📝 提示: 可以修改 vehicle_urls 列表来添加更多车辆")
    print("⚠️  注意: 请合理设置延迟时间，避免对服务器造成压力")
    
    # 开始批量爬取
    batch_scrape_vehicles(vehicle_urls, delay_range=(3, 6))


if __name__ == "__main__":
    main()
