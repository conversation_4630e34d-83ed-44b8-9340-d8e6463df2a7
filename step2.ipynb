{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import time\n", "import random\n", "import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "from fake_useragent import UserAgent\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "\n", "def get_all_csv_data(folder_path):\n", "    data_list = []\n", "    for root, _, files in os.walk(folder_path):\n", "        for file in files:\n", "            if file.endswith('.csv'):\n", "                # print(root)\n", "                # file_path = os.path.join(root, file)\n", "                file_path = f'{root}/{file}'\n", "                data_list.append(file_path)\n", "    return data_list\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "\n", "def get_all_csv_name(folder_path):\n", "    data_list = []\n", "    for root, _, files in os.walk(folder_path):\n", "        for file in files:\n", "            if file.endswith('.csv'):\n", "                # print(root)\n", "                # file_path = os.path.join(root, file)\n", "                # file_path = f'{root}/{file}'\n", "                data_list.append(file)\n", "    return data_list\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import requests\n", "import time\n", "import random\n", "from fake_useragent import UserAgent\n", "from urllib.parse import urlencode\n", "\n", "class SecureAutoPartsCrawler:\n", "    def __init__(self):\n", "        self.base_url = \"http://www.jdcsww.com/qcggs\"\n", "        self.session = requests.Session()\n", "        self.ua = UserAgent()\n", "        self._init_headers()\n", "        self.request_count = 0\n", "        self.max_retries = 3\n", "\n", "    def _init_headers(self):\n", "        \"\"\"动态生成浏览器级请求头\"\"\"\n", "        self.headers = {\n", "            \"User-Agent\": self.ua.random,\n", "            \"Accept\": \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8\",\n", "            \"Accept-Language\": \"zh-CN,zh;q=0.9\",\n", "            \"Connection\": \"keep-alive\",\n", "            \"Referer\": \"http://www.jdcsww.com/qcgg\",\n", "            \"Host\": \"www.jdcsww.com\",\n", "            \"Upgrade-Insecure-Requests\": \"1\",\n", "            \"Cache-Control\": \"max-age=0\",\n", "            \"Cookie\": \"addcontentvisit=0; __RequestVerificationToken=5h4_KxUya2IMzdqxXpgGEcmFKZijiZg3qN3He9xMnUoGqS8mWPurpkxv7CiuhkmMDuE7URWRWcm2s5lTkLko5x9tagDC2RMNjUmkScE8KEM1\"\n", "        }\n", "\n", "    def _safe_delay(self):\n", "        \"\"\"智能延迟策略\"\"\"\n", "        base_delay = random.uniform(8, 15)\n", "        # 请求次数越多，延迟越长\n", "        adaptive_delay = base_delay * (1 + self.request_count * 0.1)\n", "        jitter = random.uniform(0.8, 1.2)  # 随机扰动\n", "        time.sleep(adaptive_delay * jitter)\n", "\n", "    def _rotate_user_agent(self):\n", "        \"\"\"更换User-Agent\"\"\"\n", "        self.headers[\"User-Agent\"] = self.ua.random\n", "\n", "    def _build_params(self, ggxh):\n", "        \"\"\"构造查询参数（保持其他参数不变）\"\"\"\n", "        return {\n", "            \"ggxh\": ggxh,\n", "            \"ggpc\": \"\",\n", "            \"zwpp\": \"\",\n", "            \"clmc\": \"\",\n", "            \"fdjxh\": \"\",\n", "            \"qymc\": \"\",\n", "            \"cph\": \"\",\n", "            \"rylx\": \"\",\n", "            \"viewtype\": \"0\"\n", "        }\n", "\n", "    def fetch_page(self, ggxh):\n", "        \"\"\"获取目标页面HTML\"\"\"\n", "        self.request_count += 1\n", "        self._safe_delay()\n", "        \n", "        # 每3次请求更换UA\n", "        if self.request_count % 3 == 0:\n", "            self._rotate_user_agent()\n", "            \n", "        params = self._build_params(ggxh)\n", "        url = f\"{self.base_url}?{urlencode(params)}\"\n", "        \n", "        for attempt in range(self.max_retries):\n", "            try:\n", "                response = self.session.get(\n", "                    url,\n", "                    headers=self.headers,\n", "                    timeout=15,\n", "                    allow_redirects=False  # 禁止重定向\n", "                )\n", "                \n", "                # 反爬检测\n", "                if response.status_code == 403:\n", "                    print(\"触发封禁！更换UA并延长延迟\")\n", "                    self._rotate_user_agent()\n", "                    self._safe_delay()\n", "                    continue\n", "                    \n", "                if \"验证码\" in response.text:\n", "                    print(\"警告：触发验证码机制\")\n", "                    return None\n", "                    \n", "                response.raise_for_status()\n", "                return response.text\n", "                \n", "            except requests.exceptions.RequestException as e:\n", "                print(f\"请求失败（尝试 {attempt+1}/{self.max_retries}）: {str(e)}\")\n", "                time.sleep(2 ** attempt)  # 指数退避\n", "        return None\n", "\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["./veh/infos_换电式纯电动多用途乘用车_7.csv\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[7]\u001b[39m\u001b[32m, line 47\u001b[39m\n\u001b[32m     45\u001b[39m ggxh = ggxh_list[i]\n\u001b[32m     46\u001b[39m t0 = time.time()\n\u001b[32m---> \u001b[39m\u001b[32m47\u001b[39m html_content = \u001b[43mcrawler\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfetch_page\u001b[49m\u001b[43m(\u001b[49m\u001b[43mggxh\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     48\u001b[39m t1 = time.time()\n\u001b[32m     49\u001b[39m dtt = t1 - t0\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 59\u001b[39m, in \u001b[36mSecureAutoPartsCrawler.fetch_page\u001b[39m\u001b[34m(self, ggxh)\u001b[39m\n\u001b[32m     57\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"获取目标页面HTML\"\"\"\u001b[39;00m\n\u001b[32m     58\u001b[39m \u001b[38;5;28mself\u001b[39m.request_count += \u001b[32m1\u001b[39m\n\u001b[32m---> \u001b[39m\u001b[32m59\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_safe_delay\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     61\u001b[39m \u001b[38;5;66;03m# 每3次请求更换UA\u001b[39;00m\n\u001b[32m     62\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.request_count % \u001b[32m3\u001b[39m == \u001b[32m0\u001b[39m:\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 36\u001b[39m, in \u001b[36mSecureAutoPartsCrawler._safe_delay\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     34\u001b[39m adaptive_delay = base_delay * (\u001b[32m1\u001b[39m + \u001b[38;5;28mself\u001b[39m.request_count * \u001b[32m0.1\u001b[39m)\n\u001b[32m     35\u001b[39m jitter = random.uniform(\u001b[32m0.8\u001b[39m, \u001b[32m1.2\u001b[39m)  \u001b[38;5;66;03m# 随机扰动\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m36\u001b[39m \u001b[43mtime\u001b[49m\u001b[43m.\u001b[49m\u001b[43msleep\u001b[49m\u001b[43m(\u001b[49m\u001b[43madaptive_delay\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m \u001b[49m\u001b[43mjitter\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["# ===== 使用示例 =====\n", "import re\n", "if __name__ == \"__main__\":\n", "    crawler = SecureAutoPartsCrawler()\n", "    \n", "    # 示例：查询不同ggxh参数\n", "    #ggxh_li}st = [\"FV6462LCDEHEV\", \"FV7351KXDRT2\", \"FV8302PLMNBV\"]\n", "\n", "    \n", "    data_list = get_all_csv_data(\"./veh\")\n", "    update_dic = \"./veh_update/\"\n", "\n", "    done_list = get_all_csv_name(update_dic)\n", "\n", "    for csv_file in data_list:\n", "        \n", "\n", "        csv_name = csv_file.split('/')[-1]\n", "\n", "        if not '_纯电轿车' in csv_name:\n", "            continue\n", "\n", "        if csv_name in done_list:\n", "            continue\n", "\n", "        if csv_name == 'infos_插电_11.csv':\n", "            continue\n", "\n", "        print(csv_file)\n", "        df_csv = pd.read_csv(csv_file)\n", "\n", "        update_columns = len(df_csv.columns) + 2 - 1 - 1\n", "\n", "        df_csv['sv'] = None\n", "        df_csv['sv_all'] = None\n", "\n", "        ggxh_list = df_csv['车辆型号'].values\n", "\n", "        newest_sv = []\n", "        all_sv = []\n", "\n", "        ln = len(ggxh_list)\n", "        for i in range(len(ggxh_list)):\n", "\n", "            ggxh = ggxh_list[i]\n", "            t0 = time.time()\n", "            html_content = crawler.fetch_page(ggxh)\n", "            t1 = time.time()\n", "            dtt = t1 - t0\n", "            if html_content:\n", "                print(f\"成功获取 {i} / {ln} -- {ggxh} 数据（长度：{len(html_content)} 字符）   花费：{dtt} s\")\n", "\n", "\n", "                t1 = time.time()\n", "                pattern = r'qcggdetail\\?bh=([^&\"\\'\\s]+)\"'\n", "                # print(html_content)\n", "                bn_list = re.findall(pattern, html_content)\n", "                t2 = time.time()\n", "\n", "                if len(bn_list) > 0:\n", "\n", "                    first_item = bn_list[0]\n", "                    bn_list = list(set(bn_list))\n", "\n", "                    # newest_sv.append(first_item)\n", "                    # all_sv.append(bn_list)\n", "                #print(html_content)\n", "                # 此处添加HTML解析逻辑\n", "                else:\n", "                    first_item = None\n", "                    print(f\"---未能获取 {ggxh} 数据\")\n", "                    # newest_sv.append(None)\n", "                    # all_sv.append([])\n", "                    bn_list = []\n", "                # 此处添加HTML解析逻辑\n", "            else:\n", "                print(f\"未能获取 {i} / {ln} -- {ggxh} 数据\")\n", "                first_item = None\n", "                # newest_sv.append(None)\n", "                # all_sv.append([])\n", "                bn_list = []\n", "            \n", "            t3 = time.time()\n", "            df_csv.iloc[i, update_columns] = first_item\n", "            t4 = time.time()\n", "            df_csv.iloc[i, update_columns+1] = str(bn_list)\n", "            t5 = time.time()\n", "            df_csv.to_csv(f\"{update_dic}{csv_name}\", index=False)\n", "            t6  = time.time()\n", "\n", "            dtt1 = t2 - t1\n", "            print(f\"{bn_list}: 匹配-{dtt1}，更新-{t4-t3}-{t5-t4}， 保存：{t6-t5}\")\n", "\n", "\n", "        print(f'完成{csv_name}数据查询')\n", "\n", "        if not i == ln-1:\n", "            raa = random.ran<PERSON>t(80, 160)\n", "            time.sleep(raa)\n", "            print('sleep-----------------------------')\n", "        \n", "\n", "\n", "\n", "        break\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}