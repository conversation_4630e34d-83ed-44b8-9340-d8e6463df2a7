{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import requests\n", "import time\n", "import random \n", "import pandas as pd\n", "\n", "# 1. 定义目标URL和请求头\n", "url = \"https://app.miit-eidc.org.cn/miitxxgk/gonggao/xxgk/doCpQuery\"\n", "headers = {\n", "    \"Accept\": \"application/json, text/javascript, */*\",\n", "    \"Accept-Encoding\": \"gzip, deflate, br, zstd\",\n", "    \"Accept-Language\": \"zh-CN,zh;q=0.9\",\n", "    \"Connection\": \"keep-alive\",\n", "    \"Content-Type\": \"application/x-www-form-urlencoded\",  # 表单数据格式\n", "    \"Cookie\": \"__snaker__id=Jx2Tkz1vPBqyPf9n; JSESSIONID=2C869A7EC49544A24250AE8DCAA03780; wzws_sessionid=oGiSGIKCMTJlZmNigWNlNjliOIAxMTcuMTQ0LjY5LjEyMQ==; gdxidpyhxdE=TBxaQSp9mjhNqUMcld%2BnffJNIEuJsld8%2Bk2w78qbAy%5CGQVND%2B1B%5CCB3KmBSxV9b2bTUCGPq38jrYltX%2FLGZGJ7BQBS7rbD2W003RHvo0brYHYJZ70yn7bUvgwwfdcv47dfaJENPHJcxCUXqPIMQbOxVElppzZuqSnxrLD%5CL57gIvRTZ8%3A1754411190636\",  # 替换为完整Cookie\n", "    \"Host\": \"app.miit-eidc.org.cn\",\n", "    \"Origin\": \"https://app.miit-eidc.org.cn\",\n", "    \"Referer\": \"https://app.miit-eidc.org.cn/miitxxgk/gonggao_xxgk/index_ggcp.html\",\n", "    \"User-Agent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\n", "    \"Sec-Fetch-Dest\": \"empty\",\n", "    \"Sec-Fetch-Mode\": \"cors\",\n", "    \"Sec-Fetch-Site\": \"same-origin\"\n", "}\n", "\n", "# 2. 构造请求体（示例参数，需根据实际需求修改）\n", "pagenum = 1\n", "chunduan_payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E7%25BA%25AF%25E7%2594%25B5&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={pagenum}'\n", "chadian_payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E6%258F%2592%25E7%2594%25B5&scdz=&cplb=0&cxtype=&pageSize=10&pageNum=1'\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def get_data(veh_type, begin_page = 1):\n", "\n", "\n", "    session = requests.Session()\n", "    session.headers.update(headers)\n", "\n", "\n", "\n", "\n", "    total_count = 0\n", "    final_data_list = []\n", "    # 3. 发送POST请求\n", "    try:\n", "\n", "    # https://app.miit-eidc.org.cn/miitxxgk/gonggao/xxgk/queryCpData?dataTag=Z&gid=Y7123907&pc=347\n", "\n", "        current_page = begin_page\n", "\n", "        is_continue = True\n", "        while is_continue:\n", "\n", "\n", "            if (current_page - begin_page) == 50:\n", "                print('完成50次抓取，主动暂停')\n", "                break\n", "\n", "            if veh_type == '纯电乘用车':\n", "\n", "                # payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E7%25BA%25AF%25E7%2594%25B5&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={current_page}'\n", "\n", "                payload = f\"qymc=&pc=&cpsb=&clxh=&clmc=%25E7%25BA%25AF%25E7%2594%25B5%25E5%258A%25A8%25E5%25A4%259A%25E7%2594%25A8%25E9%2580%2594%25E4%25B9%2598%25E7%2594%25A8%25E8%25BD%25A6&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={current_page}\"\n", "            elif veh_type == '纯电轿车':\n", "                payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E7%25BA%25AF%25E7%2594%25B5%25E5%258A%25A8%25E8%25BD%25BF%25E8%25BD%25A6&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={current_page}'\n", "            elif veh_type == '换电式纯电动多用途乘用车':\n", "                print(veh_type)\n", "                payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%2520%25E6%258D%25A2%25E7%2594%25B5%25E5%25BC%258F%25E7%25BA%25AF%25E7%2594%25B5%25E5%258A%25A8%25E5%25A4%259A%25E7%2594%25A8%25E9%2580%2594%25E4%25B9%2598%25E7%2594%25A8%25E8%25BD%25A6&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={current_page}'\n", "            \n", "            elif veh_type == \"换电式纯电动轿车\":\n", "                print(veh_type)\n", "                payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E6%258D%25A2%25E7%2594%25B5%25E5%25BC%258F%25E7%25BA%25AF%25E7%2594%25B5%25E5%258A%25A8%25E8%25BD%25BF%25E8%25BD%25A6&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={current_page}'\n", "            elif veh_type == '纯电动运动型':\n", "                payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E7%25BA%25AF%25E7%2594%25B5%25E5%258A%25A8%25E8%25BF%2590%25E5%258A%25A8%25E5%259E%258B&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={current_page}'\n", "            else:\n", "                payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E6%258F%2592%25E7%2594%25B5&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={current_page}'\n", "\n", "\n", "            # first_payload = f'qymc=&pc=&cpsb=&clxh=&clmc=%25E7%25BA%25AF%25E7%2594%25B5&scdz=&cplb=0&cxtype=&pageSize=10&pageNum={current_page}'\n", "            # response = requests.post(\n", "            #     url,\n", "            #     data=payload,  # 使用 data 参数发送表单数据\n", "            #     headers=headers,\n", "            #     timeout=10,  # 超时设置\n", "            #     # proxies=next(proxies),\n", "            # )\n", "\n", "            response = session.post(url, data=payload, timeout=15)\n", "            response.raise_for_status()  # 检查HTTP错误\n", "\n", "\n", "            # 4. 处理响应（假设返回JSON）\n", "            if response.status_code == 200:\n", "                result = response.json()  # 解析JSON响应\n", "                total_page = result['countResult']['totalPage']\n", "                current_page_get = result['countResult']['pageNum']\n", "                # print(result)\n", "\n", "                print(f'当前页: {current_page_get}, 总页数: {total_page}，总数: {total_count}---------------------------------')\n", "\n", "                if current_page_get == total_page:\n", "                    is_continue = False\n", "\n", "\n", "                data_list = result['cpList']\n", "                for item in data_list:\n", "                    clmc = item['clmc']\n", "                    if '底盘' in clmc:\n", "                        continue\n", "                    else:\n", "                        if '轿车' in clmc or '乘用车' in clmc:\n", "                            final_data_list.append([item['clmc'], item['cpsb'], item['clxh'], item['qymc'], item['dataTag'], item['gid'], item['pc']])\n", "                            # print(final_data_list[-1])\n", "                            total_count += 1\n", "\n", "                if len(final_data_list) > 100:\n", "                    df = pd.DataFrame(final_data_list, columns=['车辆名称', '中文品牌', '车辆型号', '公司名称', 'tag', 'gid', 'pc'])\n", "                    df.to_csv(f'./veh/infos_{veh_type}_{current_page}.csv', index=False)\n", "                    final_data_list = []\n", "\n", "                    # \n", "                    print('sleep----------------------------------------------------------------------------')\n", "                    time.sleep(300)\n", "\n", "                if (current_page - begin_page) % 20 == 0:\n", "                    print('sleep----------------------------------------------------------------------------')\n", "                    time.sleep(30)\n", "\n", "\n", "\n", "                # # 车辆名称\n", "                # clmc_list = [item['clmc'] for item in data_list]\n", "                # # 中文品牌\n", "                # cpsb_list = [item['cpsb'] for item in data_list]\n", "                # # 车辆型号\n", "                # clxh_list = [item['clxh'] for item in data_list]\n", "                # # 公司名称\n", "                # qymc_list = [item['qymc'] for item in data_list]\n", "\n", "            elif response.status_code == 429:\n", "                print(\"触发频率限制，等待冷却...\")\n", "                time.sleep(300)  # 延长等待时间\n", "                continue\n", "            # elif response.status_code == 403:\n", "            #     print(\"IP被封禁，切换代理\")\n", "            #     current_proxy = next(proxies)  # 强制切换代理\n", "            #     continue\n", "            else:\n", "                print(f\"请求失败！状态码: {response.status_code}, 响应文本: {response.text}\")\n", "\n", "            # 在每次请求前增加动态延迟\n", "            min_delay = 10\n", "            max_delay = 25\n", "            jitter = random.uniform(0.5, 1.5)  # 随机扰动因子\n", "            delay = random.randint(min_delay, max_delay) * jitter\n", "            \n", "            current_page += 1\n", "            time.sleep(delay)\n", "\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"请求异常: {current_page} {e} \")\n", "\n", "    if len(final_data_list) > 0:\n", "        df = pd.DataFrame(final_data_list, columns=['车辆名称', '中文品牌', '车辆型号', '公司名称', 'tag', 'gid', 'pc'])\n", "        df.to_csv(f'./veh/infos_{veh_type}_{current_page}.csv', index=False)\n", "\n", "    print(f'找到{total_count}个{veh_type}车数据结果')"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前页: 1, 总页数: 6，总数: 0---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 2, 总页数: 6，总数: 10---------------------------------\n", "当前页: 3, 总页数: 6，总数: 20---------------------------------\n", "当前页: 4, 总页数: 6，总数: 30---------------------------------\n", "当前页: 5, 总页数: 6，总数: 40---------------------------------\n", "当前页: 6, 总页数: 6，总数: 50---------------------------------\n", "找到55个纯电动运动型车数据结果\n"]}], "source": ["get_data('纯电动运动型')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["换电式纯电动多用途乘用车\n", "当前页: 1, 总页数: 6，总数: 0---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "换电式纯电动多用途乘用车\n", "当前页: 2, 总页数: 6，总数: 10---------------------------------\n", "换电式纯电动多用途乘用车\n", "当前页: 3, 总页数: 6，总数: 20---------------------------------\n", "换电式纯电动多用途乘用车\n", "当前页: 4, 总页数: 6，总数: 30---------------------------------\n", "换电式纯电动多用途乘用车\n", "当前页: 5, 总页数: 6，总数: 40---------------------------------\n", "换电式纯电动多用途乘用车\n", "当前页: 6, 总页数: 6，总数: 50---------------------------------\n", "找到57个换电式纯电动多用途乘用车车数据结果\n"]}], "source": ["get_data('换电式纯电动多用途乘用车')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["换电式纯电动轿车\n", "当前页: 1, 总页数: 6，总数: 0---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "换电式纯电动轿车\n", "当前页: 2, 总页数: 6，总数: 10---------------------------------\n", "换电式纯电动轿车\n", "当前页: 3, 总页数: 6，总数: 20---------------------------------\n", "换电式纯电动轿车\n", "当前页: 4, 总页数: 6，总数: 30---------------------------------\n", "换电式纯电动轿车\n", "当前页: 5, 总页数: 6，总数: 40---------------------------------\n", "换电式纯电动轿车\n", "当前页: 6, 总页数: 6，总数: 50---------------------------------\n", "找到53个换电式纯电动轿车车数据结果\n"]}], "source": ["get_data('换电式纯电动轿车')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前页: 193, 总页数: 206，总数: 0---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 194, 总页数: 206，总数: 10---------------------------------\n", "当前页: 195, 总页数: 206，总数: 20---------------------------------\n", "当前页: 196, 总页数: 206，总数: 30---------------------------------\n", "当前页: 197, 总页数: 206，总数: 40---------------------------------\n", "当前页: 198, 总页数: 206，总数: 50---------------------------------\n", "当前页: 199, 总页数: 206，总数: 60---------------------------------\n", "当前页: 200, 总页数: 206，总数: 70---------------------------------\n", "当前页: 201, 总页数: 206，总数: 80---------------------------------\n", "当前页: 202, 总页数: 206，总数: 90---------------------------------\n", "当前页: 203, 总页数: 206，总数: 100---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 204, 总页数: 206，总数: 110---------------------------------\n", "当前页: 205, 总页数: 206，总数: 120---------------------------------\n", "当前页: 206, 总页数: 206，总数: 130---------------------------------\n", "找到132个纯电轿车车数据结果\n"]}], "source": ["get_data('纯电轿车', 193)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前页: 63, 总页数: 130，总数: 0---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 64, 总页数: 130，总数: 10---------------------------------\n", "当前页: 65, 总页数: 130，总数: 20---------------------------------\n", "当前页: 66, 总页数: 130，总数: 30---------------------------------\n", "当前页: 67, 总页数: 130，总数: 40---------------------------------\n", "当前页: 68, 总页数: 130，总数: 50---------------------------------\n", "当前页: 69, 总页数: 130，总数: 60---------------------------------\n", "当前页: 70, 总页数: 130，总数: 70---------------------------------\n", "当前页: 71, 总页数: 130，总数: 80---------------------------------\n", "当前页: 72, 总页数: 130，总数: 90---------------------------------\n", "当前页: 73, 总页数: 130，总数: 100---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 74, 总页数: 130，总数: 110---------------------------------\n", "当前页: 75, 总页数: 130，总数: 120---------------------------------\n", "当前页: 76, 总页数: 130，总数: 130---------------------------------\n", "当前页: 77, 总页数: 130，总数: 140---------------------------------\n", "当前页: 78, 总页数: 130，总数: 150---------------------------------\n", "当前页: 79, 总页数: 130，总数: 160---------------------------------\n", "当前页: 80, 总页数: 130，总数: 170---------------------------------\n", "当前页: 81, 总页数: 130，总数: 180---------------------------------\n", "当前页: 82, 总页数: 130，总数: 190---------------------------------\n", "当前页: 83, 总页数: 130，总数: 200---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 84, 总页数: 130，总数: 210---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 85, 总页数: 130，总数: 220---------------------------------\n", "当前页: 86, 总页数: 130，总数: 230---------------------------------\n", "当前页: 87, 总页数: 130，总数: 240---------------------------------\n", "当前页: 88, 总页数: 130，总数: 250---------------------------------\n", "当前页: 89, 总页数: 130，总数: 260---------------------------------\n", "当前页: 90, 总页数: 130，总数: 270---------------------------------\n", "当前页: 91, 总页数: 130，总数: 280---------------------------------\n", "当前页: 92, 总页数: 130，总数: 290---------------------------------\n", "当前页: 93, 总页数: 130，总数: 300---------------------------------\n", "当前页: 94, 总页数: 130，总数: 310---------------------------------\n", "当前页: 95, 总页数: 130，总数: 320---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 96, 总页数: 130，总数: 330---------------------------------\n", "当前页: 97, 总页数: 130，总数: 340---------------------------------\n", "当前页: 98, 总页数: 130，总数: 350---------------------------------\n", "当前页: 99, 总页数: 130，总数: 360---------------------------------\n", "当前页: 100, 总页数: 130，总数: 370---------------------------------\n", "当前页: 101, 总页数: 130，总数: 380---------------------------------\n", "当前页: 102, 总页数: 130，总数: 390---------------------------------\n", "当前页: 103, 总页数: 130，总数: 400---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 104, 总页数: 130，总数: 410---------------------------------\n", "当前页: 105, 总页数: 130，总数: 420---------------------------------\n", "当前页: 106, 总页数: 130，总数: 430---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 107, 总页数: 130，总数: 440---------------------------------\n", "当前页: 108, 总页数: 130，总数: 450---------------------------------\n", "当前页: 109, 总页数: 130，总数: 460---------------------------------\n", "当前页: 110, 总页数: 130，总数: 470---------------------------------\n", "当前页: 111, 总页数: 130，总数: 480---------------------------------\n", "当前页: 112, 总页数: 130，总数: 490---------------------------------\n", "当前页: 113, 总页数: 130，总数: 500---------------------------------\n", "当前页: 114, 总页数: 130，总数: 510---------------------------------\n", "当前页: 115, 总页数: 130，总数: 520---------------------------------\n", "当前页: 116, 总页数: 130，总数: 530---------------------------------\n", "当前页: 117, 总页数: 130，总数: 540---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 118, 总页数: 130，总数: 550---------------------------------\n", "当前页: 119, 总页数: 130，总数: 560---------------------------------\n", "当前页: 120, 总页数: 130，总数: 570---------------------------------\n", "当前页: 121, 总页数: 130，总数: 580---------------------------------\n", "当前页: 122, 总页数: 130，总数: 590---------------------------------\n", "当前页: 123, 总页数: 130，总数: 600---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 124, 总页数: 130，总数: 610---------------------------------\n", "当前页: 125, 总页数: 130，总数: 620---------------------------------\n", "当前页: 126, 总页数: 130，总数: 630---------------------------------\n", "当前页: 127, 总页数: 130，总数: 640---------------------------------\n", "当前页: 128, 总页数: 130，总数: 650---------------------------------\n", "sleep----------------------------------------------------------------------------\n", "当前页: 129, 总页数: 130，总数: 660---------------------------------\n", "当前页: 130, 总页数: 130，总数: 670---------------------------------\n", "找到675个纯电乘用车车数据结果\n"]}], "source": ["get_data('纯电乘用车', 63)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前页: 211, 总页数: 213，总数: 0---------------------------------\n", "当前页: 212, 总页数: 213，总数: 10---------------------------------\n", "当前页: 213, 总页数: 213，总数: 16---------------------------------\n", "找到16个插电车数据结果\n"]}], "source": ["get_data('插电')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}