import json
import csv
import pandas as pd
from datetime import datetime
import os


def flatten_json(data, parent_key='', sep='_'):
    """
    将嵌套的JSON数据扁平化
    """
    items = []
    if isinstance(data, dict):
        for k, v in data.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(flatten_json(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                # 处理列表数据
                if v and isinstance(v[0], list):
                    # 处理表格数据
                    for i, row in enumerate(v):
                        if isinstance(row, list):
                            for j, cell in enumerate(row):
                                items.append((f"{new_key}_table_{i}_col_{j}", cell))
                        else:
                            items.append((f"{new_key}_{i}", row))
                else:
                    for i, item in enumerate(v):
                        items.append((f"{new_key}_{i}", item))
            else:
                items.append((new_key, v))
    elif isinstance(data, list):
        for i, item in enumerate(data):
            items.extend(flatten_json(item, f"{parent_key}_{i}", sep=sep).items())
    else:
        items.append((parent_key, data))
    
    return dict(items)


def extract_key_vehicle_info(json_data):
    """
    提取关键车辆信息到结构化格式
    """
    vehicle_info = {}

    # 基本信息
    vehicle_info['车辆编号'] = json_data.get('vehicle_id', '')
    # vehicle_info['爬取时间'] = json_data.get('scrape_time', '')
    # vehicle_info['基础URL'] = json_data.get('base_url', '')
    vehicle_info['状态'] = json_data.get('status', '')

    # 图片信息
    if 'images' in json_data.get('data', {}):
        images = json_data['data']['images']
        vehicle_info['公告批次'] = images.get('PC', '')
        vehicle_info['车辆公告编号'] = images.get('CLGGBH', '')
        # vehicle_info['图片1'] = images.get('IMG1', '')
        # vehicle_info['图片2'] = images.get('IMG2', '')
        # vehicle_info['图片3'] = images.get('IMG3', '')

    # 页面内容信息
    if 'page_content' in json_data.get('data', {}):
        page_content = json_data['data']['page_content']
        vehicle_info['车辆标题'] = page_content.get('title', '')

        # 解析表格数据
        tables = page_content.get('tables', [])
        if len(tables) > 1:  # 第二个表格通常包含详细信息
            main_table = tables[1]

            # 提取关键信息
            for row in main_table:
                # print(row)

                if len(row) == 1:

                    if '储能装置种类' in row[0]:
                        # idx = row.index('其他')
                        # if idx + 1 < len(row):
                        vehicle_info['其他'] = row[0]

                if len(row) >= 2:
                    if '车型：' in row:
                        idx = row.index('车型：')
                        if idx + 1 < len(row):
                            vehicle_info['车型：'] = row[idx + 1]

                    if '英文品牌：' in row:
                        idx = row.index('英文品牌：')
                        if idx + 1 < len(row):
                            vehicle_info['英文品牌：'] = row[idx + 1]

                    if '公告批次：' in row:
                        idx = row.index('公告批次：')
                        if idx + 1 < len(row):
                            vehicle_info['公告批次'] = row[idx + 1]

                    if '发布日期：' in row:
                        idx = row.index('发布日期：')
                        if idx + 1 < len(row):
                            vehicle_info['发布日期'] = row[idx + 1]

                    if '产品号：' in row:
                        idx = row.index('产品号：')
                        if idx + 1 < len(row):
                            vehicle_info['产品号'] = row[idx + 1]

                    if '中文品牌：' in row:
                        idx = row.index('中文品牌：')
                        if idx + 1 < len(row):
                            vehicle_info['中文品牌'] = row[idx + 1]

                    if '公告型号：' in row:
                        idx = row.index('公告型号：')
                        if idx + 1 < len(row):
                            vehicle_info['公告型号'] = row[idx + 1]

                    if '企业名称：' in row:
                        idx = row.index('企业名称：')
                        if idx + 1 < len(row):
                            vehicle_info['企业名称'] = row[idx + 1]

                    if '产品ID号：' in row:
                        idx = row.index('产品ID号：')
                        if idx + 1 < len(row):
                            vehicle_info['产品ID号'] = row[idx + 1]

                    if '车辆总质量(Kg)：' in row:
                        idx = row.index('车辆总质量(Kg)：')
                        if idx + 1 < len(row):
                            vehicle_info['车辆总质量(Kg)'] = row[idx + 1]

                    if '车辆整备质量(Kg)：' in row:
                        idx = row.index('车辆整备质量(Kg)：')
                        if idx + 1 < len(row):
                            vehicle_info['车辆整备质量(Kg)'] = row[idx + 1]

                    if '最高车速(Km/h)：' in row:
                        idx = row.index('最高车速(Km/h)：')
                        if idx + 1 < len(row):
                            vehicle_info['最高车速(Km/h)'] = row[idx + 1]

                    if '车辆轴距(mm)：' in row:
                        idx = row.index('车辆轴距(mm)：')
                        if idx + 1 < len(row):
                            vehicle_info['车辆轴距(mm)'] = row[idx + 1]

                    if '外形尺寸长Ⅹ宽Ⅹ高(mm)：' in row:
                        idx = row.index('外形尺寸长Ⅹ宽Ⅹ高(mm)：')
                        if idx + 1 < len(row):
                            vehicle_info['外形尺寸(mm)'] = row[idx + 1]

                    if '车辆轮胎规格型号：' in row:
                        idx = row.index('车辆轮胎规格型号：')
                        if idx + 1 < len(row):
                            vehicle_info['轮胎规格型号'] = row[idx + 1]

                    if '公告状态：' in row:
                        idx = row.index('公告状态：')
                        if idx + 1 < len(row):
                            vehicle_info['公告状态'] = row[idx + 1]

                    if '公告生效日期：' in row:
                        idx = row.index('公告生效日期：')
                        if idx + 1 < len(row):
                            vehicle_info['公告生效日期'] = row[idx + 1]

                    if '车辆识别代码：' in row:
                        idx = row.index('车辆识别代码：')
                        if idx + 1 < len(row):
                            vehicle_info['车辆识别代码'] = row[idx + 1]

                    if '动力类型：' in row:
                        idx = row.index('动力类型：')
                        if idx + 1 < len(row):
                            vehicle_info['动力类型'] = row[idx + 1]

                    if '续驶里程(工况法)KM：' in row:
                        idx = row.index('续驶里程(工况法)KM：')
                        if idx + 1 < len(row):
                            vehicle_info['续驶里程(工况法)KM'] = row[idx + 1]

                    if '电芯类型：' in row:
                        idx = row.index('电芯类型：')
                        if idx + 1 < len(row):
                            vehicle_info['电芯类型：'] = row[idx + 1]

                    if '电芯型号：' in row:
                        idx = row.index('电芯型号：')
                        if idx + 1 < len(row):
                            vehicle_info['电芯型号：'] = row[idx + 1]

                    if '电芯形状：' in row:
                        idx = row.index('电芯形状：')
                        if idx + 1 < len(row):
                            vehicle_info['电芯形状：'] = row[idx + 1]


                    if '电芯尺寸：' in row:
                        idx = row.index('电芯尺寸：')
                        if idx + 1 < len(row):
                            vehicle_info['电芯尺寸：'] = row[idx + 1]
                    if '电芯电压(v)：' in row:
                        idx = row.index('电芯电压(v)：')
                        if idx + 1 < len(row):
                            vehicle_info['电芯电压(v)：'] = row[idx + 1]

                    if '电芯容量(Ah)：' in row:
                        idx = row.index('电芯容量(Ah)：')
                        if idx + 1 < len(row):
                            vehicle_info['电芯容量(Ah)：'] = row[idx + 1]
                    if '电芯电量(wh)：' in row:
                        idx = row.index('电芯电量(wh)：')
                        if idx + 1 < len(row):
                            vehicle_info['电芯电量(wh)：'] = row[idx + 1]
                    if '电芯数量(个)：' in row:
                        idx = row.index('电芯数量(个)：')
                        if idx + 1 < len(row):
                            vehicle_info['电芯数量(个)：'] = row[idx + 1]
                    if '电芯生产企业：' in row:
                        idx = row.index('电芯生产企业：')
                        if idx + 1 < len(row):
                            vehicle_info['电芯生产企业：'] = row[idx + 1]   

                    if '电池电量(KWh)：' in row:
                        idx = row.index('电池电量(KWh)：')
                        if idx + 1 < len(row):
                            vehicle_info['电池电量(KWh)：'] = row[idx + 1]
                    if '电池质量(kg)：' in row:
                        idx = row.index('电池质量(kg)：')
                        if idx + 1 < len(row):
                            vehicle_info['电池质量(kg)：'] = row[idx + 1]
                    if '电池管理系统BMS：' in row:
                        idx = row.index('电池管理系统BMS：')
                        if idx + 1 < len(row):
                            vehicle_info['电池管理系统BMS：'] = row[idx + 1]
                    if '电池生产企业：' in row:
                        idx = row.index('电池生产企业：')
                        if idx + 1 < len(row):
                            vehicle_info['电池生产企业：'] = row[idx + 1]
                    if '电池组合方式：' in row:
                        idx = row.index('电池组合方式：')
                        if idx + 1 < len(row):
                            vehicle_info['电池组合方式：'] = row[idx + 1]
                    if '电机型号：' in row:
                        idx = row.index('电机型号：')
                        if idx + 1 < len(row):
                            vehicle_info['电机型号：'] = row[idx + 1]
                    if '电机类型：' in row:
                        idx = row.index('电机类型：')
                        if idx + 1 < len(row):
                            vehicle_info['电机类型：'] = row[idx + 1]
                    if '电机冷却方式：' in row:
                        idx = row.index('电机冷却方式：')
                        if idx + 1 < len(row):
                            vehicle_info['电机冷却方式：'] = row[idx + 1]
                    if '电机生产企业：' in row:
                        idx = row.index('电机生产企业：')
                        if idx + 1 < len(row):
                            vehicle_info['电机生产企业：'] = row[idx + 1]
                    if '电机数量(个)：' in row:
                        idx = row.index('电机数量(个)：')
                        if idx + 1 < len(row):
                            vehicle_info['电机数量(个)：'] = row[idx + 1]
                    if '电控供应商：' in row:
                        idx = row.index('电控供应商：')
                        if idx + 1 < len(row):
                            vehicle_info['电控供应商：'] = row[idx + 1]
                    if '车载DC/DC变换器：' in row:
                        idx = row.index('车载DC/DC变换器：')
                        if idx + 1 < len(row):
                            vehicle_info['车载DC/DC变换器：'] = row[idx + 1]

                    if '车辆耗油(L/100Km)：' in row:
                        idx = row.index('车辆耗油(L/100Km)：')
                        if idx + 1 < len(row):
                            vehicle_info['车辆耗油(L/100Km)：'] = row[idx + 1]


    return vehicle_info


def json_to_csv_simple(json_file, csv_file):
    """
    将JSON转换为简化的CSV格式（只包含关键信息）
    """
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取关键信息
        vehicle_info = extract_key_vehicle_info(data)
        
        # 创建DataFrame
        df = pd.DataFrame([vehicle_info])
        
        # 保存为CSV
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        print(f"✓ 简化CSV文件已生成: {csv_file}")
        print(f"  包含 {len(vehicle_info)} 个关键字段")
        
        return True
        
    except Exception as e:
        print(f"✗ 转换失败: {str(e)}")
        return False


def json_to_csv_full(json_file, csv_file):
    """
    将JSON转换为完整的CSV格式（包含所有数据）
    """
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 扁平化JSON数据
        flattened_data = flatten_json(data)
        
        # 创建DataFrame
        df = pd.DataFrame([flattened_data])
        
        # 保存为CSV
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        print(f"✓ 完整CSV文件已生成: {csv_file}")
        print(f"  包含 {len(flattened_data)} 个字段")
        
        return True
        
    except Exception as e:
        print(f"✗ 转换失败: {str(e)}")
        return False


def batch_convert(input_dir='.', output_dir='csv_output'):
    """
    批量转换目录中的所有JSON文件
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    json_files = [f for f in os.listdir(input_dir) if f.endswith('.json')]
    
    if not json_files:
        print("未找到JSON文件")
        return
    
    print(f"找到 {len(json_files)} 个JSON文件")
    
    for json_file in json_files:
        base_name = os.path.splitext(json_file)[0]
        
        # 生成简化版CSV
        simple_csv = os.path.join(output_dir, f"{base_name}_simple.csv")
        json_to_csv_simple(os.path.join(input_dir, json_file), simple_csv)
        
        # 生成完整版CSV
        full_csv = os.path.join(output_dir, f"{base_name}_full.csv")
        json_to_csv_full(os.path.join(input_dir, json_file), full_csv)
        
        # print()




import requests
import json
import time
import random
from urllib.parse import urljoin


class SuccessfulVehicleScraper:
    def __init__(self):
        self.session = requests.Session()
        self.setup_real_session()
        
    def setup_real_session(self):
        """使用真实浏览器的请求头设置会话"""
        # 基于用户提供的真实请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'X-Requested-With': 'XMLHttpRequest',
            'Priority': 'u=1, i'
        })
        
    def get_verification_token(self, base_url="http://www.jdcsww.com"):
        """获取验证令牌"""
        try:
            # print("正在获取验证令牌...")
            
            # 首先访问主页面获取token
            main_url = f"{base_url}/qcggdetail?bh=SV2025072804092"
            response = self.session.get(main_url, timeout=15)
            
            if response.status_code == 200:
                # 从响应中提取token（通常在cookie或页面中）
                cookies = response.cookies
                for cookie in cookies:
                    if 'RequestVerificationToken' in cookie.name:
                        # print(f"找到验证令牌: {cookie.value[:20]}...")
                        return cookie.value
                
                # 如果cookie中没有，尝试从页面内容中提取
                if '__RequestVerificationToken' in response.text:
                    import re
                    token_match = re.search(r'__RequestVerificationToken["\']?\s*:\s*["\']([^"\']+)', response.text)
                    if token_match:
                        token = token_match.group(1)
                        # print(f"从页面提取到令牌: {token[:20]}...")
                        return token
                
                # print("使用默认令牌...")
                return "WEz6pBAF84GPJaHkxYBhOqI0v73HCET7yu9fdF1JM_chSoKiV7Uj7XN9q7zROILH3MEXdoO6OaRAJO8Ibagtan96fPXiofDJhMWNmjOOJew1"
            
        except Exception as e:
            print(f"获取令牌失败: {e}")
            return "WEz6pBAF84GPJaHkxYBhOqI0v73HCET7yu9fdF1JM_chSoKiV7Uj7XN9q7zROILH3MEXdoO6OaRAJO8Ibagtan96fPXiofDJhMWNmjOOJew1"
    
    def get_vehicle_images(self, bh="SV2025072804092", clggbh="", base_url="http://www.jdcsww.com"):
        """获取车辆图片信息（这可能包含车辆数据）"""
        try:
            # print(f"正在获取车辆图片信息: {bh}")
            
            # 设置请求头
            token = self.get_verification_token(base_url)
            
            # 设置cookie
            self.session.cookies.set('__RequestVerificationToken', token)
            self.session.cookies.set('addcontentvisit', '0')
            
            # 设置referer
            self.session.headers['Referer'] = f'{base_url}/qcggdetail?bh={bh}'
            
            # 构建API请求URL
            api_url = f"{base_url}/getimgs?bh={bh}&clggbh={clggbh}"
            
            # print(f"请求URL: {api_url}")
            
            # 发送请求
            response = self.session.get(api_url, timeout=15)
            
            # print(f"响应状态码: {response.status_code}")
            # print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    # print("✓ 成功获取JSON数据!")
                    return data
                except json.JSONDecodeError:
                    # print("响应不是JSON格式，返回文本内容")
                    return {"text_content": response.text}
            else:
                # print(f"请求失败，状态码: {response.status_code}")
                return {"error": f"HTTP {response.status_code}", "content": response.text}
                
        except Exception as e:
            # print(f"获取车辆图片信息失败: {e}")
            return {"error": str(e)}
    
    def get_vehicle_detail(self, bh="SV2025072804092", base_url="http://www.jdcsww.com"):
        """获取车辆详细信息"""
        try:
            # print(f"正在获取车辆详细信息: {bh}")
            
            # 可能的其他API端点
            api_endpoints = [
                f"{base_url}/getdetail?bh={bh}",
                f"{base_url}/api/vehicle?bh={bh}",
                f"{base_url}/qcggdetail/getdata?bh={bh}",
                f"{base_url}/vehicle/detail?bh={bh}"
            ]
            
            for api_url in api_endpoints:
                try:
                    # print(f"尝试API: {api_url}")
                    response = self.session.get(api_url, timeout=10)
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            # print(f"✓ API成功: {api_url}")
                            return data
                        except:
                            if len(response.text) > 100:  # 有实际内容
                                # print(f"✓ 获取到文本数据: {api_url}")
                                return {"text_content": response.text}
                    
                except Exception as e:
                    # print(f"API失败 {api_url}: {e}")
                    continue
            
            return None
            
        except Exception as e:
            # print(f"获取车辆详细信息失败: {e}")
            return {"error": str(e)}
    
    def scrape_vehicle_info(self, bh="SV2025072804092", base_url="http://www.jdcsww.com"):
        """主要爬取方法"""
        # print("=" * 60)
        # print("车辆信息爬虫 - 成功版本")
        # print("=" * 60)
        # print(f"目标车辆编号: {bh}")
        # print(f"目标URL: {base_url}/qcggdetail?bh={bh}")
        # print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {
            "vehicle_id": bh,
            "base_url": base_url,
            "scrape_time": time.strftime('%Y-%m-%d %H:%M:%S'),
            "status": "success",
            "data": {}
        }
        
        # 方法1: 获取图片信息
        # print("\n--- 方法1: 获取车辆图片信息 ---")
        images_data = self.get_vehicle_images(bh, "", base_url)
        if images_data and not images_data.get("error"):
            results["data"]["images"] = images_data
            # print("✓ 图片信息获取成功")
        else:
            pass
            # print("✗ 图片信息获取失败")
        
        # 方法2: 尝试其他API端点
        # print("\n--- 方法2: 尝试其他API端点 ---")
        detail_data = self.get_vehicle_detail(bh, base_url)
        if detail_data and not detail_data.get("error"):
            results["data"]["details"] = detail_data
            # print("✓ 详细信息获取成功")
        else:
            pass
            # print("✗ 详细信息获取失败")
        
        # 方法3: 直接访问页面并解析
        # print("\n--- 方法3: 直接页面访问 ---")
        page_data = self.get_page_content(bh, base_url)
        if page_data:
            results["data"]["page_content"] = page_data
            # print("✓ 页面内容获取成功")
        else:
            pass
            # print("✗ 页面内容获取失败")
        
        return results
    
    def get_page_content(self, bh, base_url="http://www.jdcsww.com"):
        """获取页面内容"""
        try:
            url = f"{base_url}/qcggdetail?bh={bh}"
            
            # 更新请求头为页面请求
            page_headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            }
            
            # 临时更新请求头
            original_headers = self.session.headers.copy()
            self.session.headers.update(page_headers)
            
            response = self.session.get(url, timeout=15)
            
            # 恢复原始请求头
            self.session.headers = original_headers
            
            if response.status_code == 200:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 提取有用信息
                page_info = {
                    "title": soup.find('title').get_text() if soup.find('title') else "",
                    "tables": [],
                    "scripts": [],
                    "forms": []
                }
                
                # 提取表格
                tables = soup.find_all('table')
                for table in tables:
                    rows = []
                    for row in table.find_all('tr'):
                        cells = [cell.get_text().strip() for cell in row.find_all(['td', 'th'])]
                        if any(cells):
                            rows.append(cells)
                    if rows:
                        page_info["tables"].append(rows)
                
                # 提取脚本中的数据
                scripts = soup.find_all('script')
                for script in scripts:
                    script_text = script.get_text()
                    if 'vehicle' in script_text.lower() or 'data' in script_text.lower():
                        page_info["scripts"].append(script_text[:500])  # 只保存前500字符
                
                return page_info
            
            return None
            
        except Exception as e:
            print(f"获取页面内容失败: {e}")
            return None
    
    def save_results(self, data, vehicle_id=None):
        """保存结果到分类文件夹"""
        import os
        
        try:
            # 获取车辆编号
            if not vehicle_id:
                vehicle_id = data.get('vehicle_id', 'unknown')
            
            # 创建文件夹
            json_dir = 'json'
            csv_dir = 'csv'
            os.makedirs(json_dir, exist_ok=True)
            os.makedirs(csv_dir, exist_ok=True)
            
            # 生成文件名
            json_filename = os.path.join(json_dir, f"vehicle_{vehicle_id}.json")
            
            # 保存JSON文件
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"\n✓ JSON文件已保存到: {json_filename}")
            
            # 自动生成CSV文件
            self.auto_generate_csv(data, vehicle_id, csv_dir)
            
            return True
        except Exception as e:
            print(f"\n✗ 保存失败: {e}")
            return False
    
    def auto_generate_csv(self, data, vehicle_id, csv_dir):
        """自动生成CSV文件到指定文件夹"""
        try:
            import pandas as pd
            import os
            
            # 提取关键信息
            vehicle_info = extract_key_vehicle_info(data)
            
            # 生成CSV文件名
            csv_filename = os.path.join(csv_dir, f"vehicle_{vehicle_id}.csv")
            
            # 创建DataFrame并保存
            df = pd.DataFrame([vehicle_info])
            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            
            # print(f"✓ CSV文件已保存到: {csv_filename}")
            # print(f"  包含 {len(vehicle_info)} 个关键字段")
            
        except ImportError:
            print("⚠️ 无法导入CSV转换模块，请手动运行 python json_to_csv.py")
        except Exception as e:
            print(f"⚠️ CSV自动生成失败: {e}")


def scrape_from_url(url):
    """从完整URL爬取车辆信息的便捷函数"""
    import re
    from urllib.parse import urlparse, parse_qs
    
    # 解析URL
    parsed = urlparse(url)
    base_url = f"{parsed.scheme}://{parsed.netloc}"
    
    # 提取车辆编号
    query_params = parse_qs(parsed.query)
    bh = query_params.get('bh', [''])[0]
    
    if not bh:
        print("❌ 无法从URL中提取车辆编号(bh参数)")
        return None
    
    print(f"📋 解析URL:")
    print(f"   基础URL: {base_url}")
    print(f"   车辆编号: {bh}")
    print(f"   完整URL: {url}")
    
    # 创建爬虫并执行
    scraper = SuccessfulVehicleScraper()
    result = scraper.scrape_vehicle_info(bh, base_url)
    
    # 保存结果
    if result and result.get('status') == 'success':
        scraper.save_results(result, bh)
    
    return result


import time
import random
import numpy as np
import math

def batch_scrape_vehicles(vehicle_urls_list, delay_range=(2, 5)):
    """
    批量爬取多个车辆信息
    
    Args:
        vehicle_urls: 车辆URL列表
        delay_range: 延迟时间范围(秒)，避免请求过快
    """
    import os
    
    # print("=" * 70)
    # print("批量车辆信息爬虫")
    # print("=" * 70)
    print(f"准备爬取 {len(vehicle_urls_list)} 个车辆信息")
    
    success_count = 0
    failed_count = 0
    success_vehicles = []
    failed_vehicles = []
    status = []
    for i, url in enumerate(vehicle_urls_list, 1):

        if not type(url) == str:
            status.append(-1)
            continue

        if 'http' in url:
            url = url
        else:
            url = f'http://www.jdcsww.com/qcggdetail?bh={url}'

        print(f"\n[{i}/{len(vehicle_urls_list)}] 正在爬取: {url}")
        
        try:
            result = scrape_from_url(url)
            
            if result and result.get('status') == 'success':
                success_count += 1
                vehicle_id = result.get('vehicle_id', 'unknown')
                success_vehicles.append(vehicle_id)
                print(f"✓ 第 {i} 个车辆爬取成功: {vehicle_id}")
                status.append(1)
            else:
                failed_count += 1
                failed_vehicles.append(url)
                print(f"✗ 第 {i} 个车辆爬取失败: 可能车辆编号不存在")
                status.append(0)
                
        except Exception as e:
            failed_count += 1
            failed_vehicles.append(url)
            print(f"✗ 第 {i} 个车辆爬取异常: {str(e)}")
            status.append(0)
        
        # 添加随机延迟，避免请求过快
        if i < len(vehicle_urls_list):  # 最后一个不需要延迟
            delay = random.uniform(delay_range[0], delay_range[1])
            print(f"⏳ 等待 {delay:.1f} 秒...")
            time.sleep(delay)
    
    print("\n" + "=" * 70)
    print("批量爬取完成")
    print("=" * 70)
    print(f"✓ 成功: {success_count} 个")
    print(f"✗ 失败: {failed_count} 个")

    
    # 显示成功爬取的车辆
    if success_vehicles:
        print(f"\n📋 成功爬取的车辆:")
        for vehicle_id in success_vehicles:
            print(f"   - {vehicle_id}")
    
    # 显示失败的URL
    if failed_vehicles:
        print(f"\n❌ 失败的车辆URL:")
        for url in failed_vehicles:
            print(f"   - {url}")
    
    # 列出实际生成的文件
    # print(f"\n📁 实际生成的文件:")
    
    # 检查JSON文件
    # json_dir = './json'
    # if os.path.exists(json_dir):
    #     json_files = [f for f in os.listdir(json_dir) if f.endswith('.json')]
    #     if json_files:
    #         print(f"   JSON文件 ({len(json_files)} 个):")
    #         for file in json_files:
    #             print(f"     - {file}")
    #     else:
    #         print(f"   JSON文件: 无")
    # else:
    #     print(f"   JSON文件夹不存在")
    
    # # 检查CSV文件
    # csv_dir = './csv'
    # if os.path.exists(csv_dir):
    #     csv_files = [f for f in os.listdir(csv_dir) if f.endswith('.csv')]
    #     if csv_files:
    #         print(f"   CSV文件 ({len(csv_files)} 个):")
    #         for file in csv_files:
    #             print(f"     - {file}")
    #     else:
    #         print(f"   CSV文件: 无")
    # else:
    #     print(f"   CSV文件夹不存在")

    return status

import os
import pandas as pd

def get_all_csv_data(folder_path):
    data_list = []
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.csv'):
                # print(root)
                # file_path = os.path.join(root, file)
                file_path = f'{root}/{file}'
                data_list.append(file_path)
    return data_list


import os
import pandas as pd

def get_all_csv_name(folder_path):
    data_list = []
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.csv'):
                # print(root)
                # file_path = os.path.join(root, file)
                # file_path = f'{root}/{file}'
                data_list.append(file)
    return data_list


def main():
    """主函数 - 批量爬取示例"""

    update_dic = "./veh_update/"
    data_list = get_all_csv_data(update_dic)


    save_dic = './step3_done/'

    done_list = get_all_csv_name(save_dic)

    for csv_file in data_list:

        csv_name = csv_file.split('/')[-1]

        if csv_name in done_list:
            continue

        # if not 'test' in csv_name:
        #     continue

        print(csv_file)
        df_csv = pd.read_csv(csv_file)

        sv_list = df_csv['sv'].values


    
        # 开始批量爬取
        status = batch_scrape_vehicles(sv_list, delay_range=(3, 6))

        df_csv['status'] = status
        df_csv.to_csv(save_dic + csv_name)

        print('完成：', csv_name)

        time.sleep(random.randint(50,70))


main()
