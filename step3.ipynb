{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import json\n", "import csv\n", "import pandas as pd\n", "from datetime import datetime\n", "import os\n", "\n", "\n", "def flatten_json(data, parent_key='', sep='_'):\n", "    \"\"\"\n", "    将嵌套的JSON数据扁平化\n", "    \"\"\"\n", "    items = []\n", "    if isinstance(data, dict):\n", "        for k, v in data.items():\n", "            new_key = f\"{parent_key}{sep}{k}\" if parent_key else k\n", "            if isinstance(v, dict):\n", "                items.extend(flatten_json(v, new_key, sep=sep).items())\n", "            elif isinstance(v, list):\n", "                # 处理列表数据\n", "                if v and isinstance(v[0], list):\n", "                    # 处理表格数据\n", "                    for i, row in enumerate(v):\n", "                        if isinstance(row, list):\n", "                            for j, cell in enumerate(row):\n", "                                items.append((f\"{new_key}_table_{i}_col_{j}\", cell))\n", "                        else:\n", "                            items.append((f\"{new_key}_{i}\", row))\n", "                else:\n", "                    for i, item in enumerate(v):\n", "                        items.append((f\"{new_key}_{i}\", item))\n", "            else:\n", "                items.append((new_key, v))\n", "    elif isinstance(data, list):\n", "        for i, item in enumerate(data):\n", "            items.extend(flatten_json(item, f\"{parent_key}_{i}\", sep=sep).items())\n", "    else:\n", "        items.append((parent_key, data))\n", "    \n", "    return dict(items)\n", "\n", "\n", "def extract_key_vehicle_info(json_data):\n", "    \"\"\"\n", "    提取关键车辆信息到结构化格式\n", "    \"\"\"\n", "    vehicle_info = {}\n", "\n", "    # 基本信息\n", "    vehicle_info['车辆编号'] = json_data.get('vehicle_id', '')\n", "    # vehicle_info['爬取时间'] = json_data.get('scrape_time', '')\n", "    # vehicle_info['基础URL'] = json_data.get('base_url', '')\n", "    vehicle_info['状态'] = json_data.get('status', '')\n", "\n", "    # 图片信息\n", "    if 'images' in json_data.get('data', {}):\n", "        images = json_data['data']['images']\n", "        vehicle_info['公告批次'] = images.get('PC', '')\n", "        vehicle_info['车辆公告编号'] = images.get('CLGGBH', '')\n", "        # vehicle_info['图片1'] = images.get('IMG1', '')\n", "        # vehicle_info['图片2'] = images.get('IMG2', '')\n", "        # vehicle_info['图片3'] = images.get('IMG3', '')\n", "\n", "    # 页面内容信息\n", "    if 'page_content' in json_data.get('data', {}):\n", "        page_content = json_data['data']['page_content']\n", "        vehicle_info['车辆标题'] = page_content.get('title', '')\n", "\n", "        # 解析表格数据\n", "        tables = page_content.get('tables', [])\n", "        if len(tables) > 1:  # 第二个表格通常包含详细信息\n", "            main_table = tables[1]\n", "\n", "            # 提取关键信息\n", "            for row in main_table:\n", "                # print(row)\n", "\n", "                if len(row) == 1:\n", "\n", "                    if '储能装置种类' in row[0]:\n", "                        # idx = row.index('其他')\n", "                        # if idx + 1 < len(row):\n", "                        vehicle_info['其他'] = row[0]\n", "\n", "                if len(row) >= 2:\n", "                    if '车型：' in row:\n", "                        idx = row.index('车型：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['车型：'] = row[idx + 1]\n", "\n", "                    if '英文品牌：' in row:\n", "                        idx = row.index('英文品牌：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['英文品牌：'] = row[idx + 1]\n", "\n", "                    if '公告批次：' in row:\n", "                        idx = row.index('公告批次：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['公告批次'] = row[idx + 1]\n", "\n", "                    if '发布日期：' in row:\n", "                        idx = row.index('发布日期：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['发布日期'] = row[idx + 1]\n", "\n", "                    if '产品号：' in row:\n", "                        idx = row.index('产品号：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['产品号'] = row[idx + 1]\n", "\n", "                    if '中文品牌：' in row:\n", "                        idx = row.index('中文品牌：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['中文品牌'] = row[idx + 1]\n", "\n", "                    if '公告型号：' in row:\n", "                        idx = row.index('公告型号：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['公告型号'] = row[idx + 1]\n", "\n", "                    if '企业名称：' in row:\n", "                        idx = row.index('企业名称：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['企业名称'] = row[idx + 1]\n", "\n", "                    if '产品ID号：' in row:\n", "                        idx = row.index('产品ID号：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['产品ID号'] = row[idx + 1]\n", "\n", "                    if '车辆总质量(Kg)：' in row:\n", "                        idx = row.index('车辆总质量(Kg)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['车辆总质量(Kg)'] = row[idx + 1]\n", "\n", "                    if '车辆整备质量(Kg)：' in row:\n", "                        idx = row.index('车辆整备质量(Kg)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['车辆整备质量(Kg)'] = row[idx + 1]\n", "\n", "                    if '最高车速(Km/h)：' in row:\n", "                        idx = row.index('最高车速(Km/h)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['最高车速(Km/h)'] = row[idx + 1]\n", "\n", "                    if '车辆轴距(mm)：' in row:\n", "                        idx = row.index('车辆轴距(mm)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['车辆轴距(mm)'] = row[idx + 1]\n", "\n", "                    if '外形尺寸长Ⅹ宽Ⅹ高(mm)：' in row:\n", "                        idx = row.index('外形尺寸长Ⅹ宽Ⅹ高(mm)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['外形尺寸(mm)'] = row[idx + 1]\n", "\n", "                    if '车辆轮胎规格型号：' in row:\n", "                        idx = row.index('车辆轮胎规格型号：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['轮胎规格型号'] = row[idx + 1]\n", "\n", "                    if '公告状态：' in row:\n", "                        idx = row.index('公告状态：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['公告状态'] = row[idx + 1]\n", "\n", "                    if '公告生效日期：' in row:\n", "                        idx = row.index('公告生效日期：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['公告生效日期'] = row[idx + 1]\n", "\n", "                    if '车辆识别代码：' in row:\n", "                        idx = row.index('车辆识别代码：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['车辆识别代码'] = row[idx + 1]\n", "\n", "                    if '动力类型：' in row:\n", "                        idx = row.index('动力类型：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['动力类型'] = row[idx + 1]\n", "\n", "                    if '续驶里程(工况法)KM：' in row:\n", "                        idx = row.index('续驶里程(工况法)KM：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['续驶里程(工况法)KM'] = row[idx + 1]\n", "\n", "                    if '电芯类型：' in row:\n", "                        idx = row.index('电芯类型：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电芯类型：'] = row[idx + 1]\n", "\n", "                    if '电芯型号：' in row:\n", "                        idx = row.index('电芯型号：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电芯型号：'] = row[idx + 1]\n", "\n", "                    if '电芯形状：' in row:\n", "                        idx = row.index('电芯形状：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电芯形状：'] = row[idx + 1]\n", "\n", "\n", "                    if '电芯尺寸：' in row:\n", "                        idx = row.index('电芯尺寸：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电芯尺寸：'] = row[idx + 1]\n", "                    if '电芯电压(v)：' in row:\n", "                        idx = row.index('电芯电压(v)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电芯电压(v)：'] = row[idx + 1]\n", "\n", "                    if '电芯容量(Ah)：' in row:\n", "                        idx = row.index('电芯容量(Ah)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电芯容量(Ah)：'] = row[idx + 1]\n", "                    if '电芯电量(wh)：' in row:\n", "                        idx = row.index('电芯电量(wh)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电芯电量(wh)：'] = row[idx + 1]\n", "                    if '电芯数量(个)：' in row:\n", "                        idx = row.index('电芯数量(个)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电芯数量(个)：'] = row[idx + 1]\n", "                    if '电芯生产企业：' in row:\n", "                        idx = row.index('电芯生产企业：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电芯生产企业：'] = row[idx + 1]   \n", "\n", "                    if '电池电量(KWh)：' in row:\n", "                        idx = row.index('电池电量(KWh)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电池电量(KWh)：'] = row[idx + 1]\n", "                    if '电池质量(kg)：' in row:\n", "                        idx = row.index('电池质量(kg)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电池质量(kg)：'] = row[idx + 1]\n", "                    if '电池管理系统BMS：' in row:\n", "                        idx = row.index('电池管理系统BMS：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电池管理系统BMS：'] = row[idx + 1]\n", "                    if '电池生产企业：' in row:\n", "                        idx = row.index('电池生产企业：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电池生产企业：'] = row[idx + 1]\n", "                    if '电池组合方式：' in row:\n", "                        idx = row.index('电池组合方式：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电池组合方式：'] = row[idx + 1]\n", "                    if '电机型号：' in row:\n", "                        idx = row.index('电机型号：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电机型号：'] = row[idx + 1]\n", "                    if '电机类型：' in row:\n", "                        idx = row.index('电机类型：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电机类型：'] = row[idx + 1]\n", "                    if '电机冷却方式：' in row:\n", "                        idx = row.index('电机冷却方式：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电机冷却方式：'] = row[idx + 1]\n", "                    if '电机生产企业：' in row:\n", "                        idx = row.index('电机生产企业：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电机生产企业：'] = row[idx + 1]\n", "                    if '电机数量(个)：' in row:\n", "                        idx = row.index('电机数量(个)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电机数量(个)：'] = row[idx + 1]\n", "                    if '电控供应商：' in row:\n", "                        idx = row.index('电控供应商：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['电控供应商：'] = row[idx + 1]\n", "                    if '车载DC/DC变换器：' in row:\n", "                        idx = row.index('车载DC/DC变换器：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['车载DC/DC变换器：'] = row[idx + 1]\n", "\n", "                    if '车辆耗油(L/100Km)：' in row:\n", "                        idx = row.index('车辆耗油(L/100Km)：')\n", "                        if idx + 1 < len(row):\n", "                            vehicle_info['车辆耗油(L/100Km)：'] = row[idx + 1]\n", "\n", "\n", "    return vehicle_info\n", "\n", "\n", "def json_to_csv_simple(json_file, csv_file):\n", "    \"\"\"\n", "    将JSON转换为简化的CSV格式（只包含关键信息）\n", "    \"\"\"\n", "    try:\n", "        with open(json_file, 'r', encoding='utf-8') as f:\n", "            data = json.load(f)\n", "        \n", "        # 提取关键信息\n", "        vehicle_info = extract_key_vehicle_info(data)\n", "        \n", "        # 创建DataFrame\n", "        df = pd.DataFrame([vehicle_info])\n", "        \n", "        # 保存为CSV\n", "        df.to_csv(csv_file, index=False, encoding='utf-8-sig')\n", "        \n", "        print(f\"✓ 简化CSV文件已生成: {csv_file}\")\n", "        print(f\"  包含 {len(vehicle_info)} 个关键字段\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"✗ 转换失败: {str(e)}\")\n", "        return False\n", "\n", "\n", "def json_to_csv_full(json_file, csv_file):\n", "    \"\"\"\n", "    将JSON转换为完整的CSV格式（包含所有数据）\n", "    \"\"\"\n", "    try:\n", "        with open(json_file, 'r', encoding='utf-8') as f:\n", "            data = json.load(f)\n", "        \n", "        # 扁平化JSON数据\n", "        flattened_data = flatten_json(data)\n", "        \n", "        # 创建DataFrame\n", "        df = pd.DataFrame([flattened_data])\n", "        \n", "        # 保存为CSV\n", "        df.to_csv(csv_file, index=False, encoding='utf-8-sig')\n", "        \n", "        print(f\"✓ 完整CSV文件已生成: {csv_file}\")\n", "        print(f\"  包含 {len(flattened_data)} 个字段\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"✗ 转换失败: {str(e)}\")\n", "        return False\n", "\n", "\n", "def batch_convert(input_dir='.', output_dir='csv_output'):\n", "    \"\"\"\n", "    批量转换目录中的所有JSON文件\n", "    \"\"\"\n", "    if not os.path.exists(output_dir):\n", "        os.makedirs(output_dir)\n", "    \n", "    json_files = [f for f in os.listdir(input_dir) if f.endswith('.json')]\n", "    \n", "    if not json_files:\n", "        print(\"未找到JSON文件\")\n", "        return\n", "    \n", "    print(f\"找到 {len(json_files)} 个JSON文件\")\n", "    \n", "    for json_file in json_files:\n", "        base_name = os.path.splitext(json_file)[0]\n", "        \n", "        # 生成简化版CSV\n", "        simple_csv = os.path.join(output_dir, f\"{base_name}_simple.csv\")\n", "        json_to_csv_simple(os.path.join(input_dir, json_file), simple_csv)\n", "        \n", "        # 生成完整版CSV\n", "        full_csv = os.path.join(output_dir, f\"{base_name}_full.csv\")\n", "        json_to_csv_full(os.path.join(input_dir, json_file), full_csv)\n", "        \n", "        # print()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "import time\n", "import random\n", "from urllib.parse import urljoin\n", "\n", "\n", "class SuccessfulVehicleScraper:\n", "    def __init__(self):\n", "        self.session = requests.Session()\n", "        self.setup_real_session()\n", "        \n", "    def setup_real_session(self):\n", "        \"\"\"使用真实浏览器的请求头设置会话\"\"\"\n", "        # 基于用户提供的真实请求头\n", "        self.session.headers.update({\n", "            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',\n", "            'Accept': 'application/json, text/javascript, */*; q=0.01',\n", "            'Accept-Language': 'zh-CN,zh;q=0.9',\n", "            'Accept-Encoding': 'gzip, deflate, br, zstd',\n", "            'Cache-Control': 'no-cache',\n", "            'Pragma': 'no-cache',\n", "            'Sec-Ch-Ua': '\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"',\n", "            'Sec-Ch-Ua-Mobile': '?0',\n", "            'Sec-Ch-Ua-Platform': '\"Windows\"',\n", "            'Se<PERSON>-<PERSON><PERSON>-Dest': 'empty',\n", "            'Sec-Fetch-Mode': 'cors',\n", "            'Sec-Fetch-Site': 'same-origin',\n", "            'X-Requested-With': 'XMLHttpRequest',\n", "            'Priority': 'u=1, i'\n", "        })\n", "        \n", "    def get_verification_token(self, base_url=\"http://www.jdcsww.com\"):\n", "        \"\"\"获取验证令牌\"\"\"\n", "        try:\n", "            # print(\"正在获取验证令牌...\")\n", "            \n", "            # 首先访问主页面获取token\n", "            main_url = f\"{base_url}/qcggdetail?bh=SV2025072804092\"\n", "            response = self.session.get(main_url, timeout=15)\n", "            \n", "            if response.status_code == 200:\n", "                # 从响应中提取token（通常在cookie或页面中）\n", "                cookies = response.cookies\n", "                for cookie in cookies:\n", "                    if 'RequestVerificationToken' in cookie.name:\n", "                        # print(f\"找到验证令牌: {cookie.value[:20]}...\")\n", "                        return cookie.value\n", "                \n", "                # 如果cookie中没有，尝试从页面内容中提取\n", "                if '__RequestVerificationToken' in response.text:\n", "                    import re\n", "                    token_match = re.search(r'__RequestVerificationToken[\"\\']?\\s*:\\s*[\"\\']([^\"\\']+)', response.text)\n", "                    if token_match:\n", "                        token = token_match.group(1)\n", "                        # print(f\"从页面提取到令牌: {token[:20]}...\")\n", "                        return token\n", "                \n", "                # print(\"使用默认令牌...\")\n", "                return \"WEz6pBAF84GPJaHkxYBhOqI0v73HCET7yu9fdF1JM_chSoKiV7Uj7XN9q7zROILH3MEXdoO6OaRAJO8Ibagtan96fPXiofDJhMWNmjOOJew1\"\n", "            \n", "        except Exception as e:\n", "            print(f\"获取令牌失败: {e}\")\n", "            return \"WEz6pBAF84GPJaHkxYBhOqI0v73HCET7yu9fdF1JM_chSoKiV7Uj7XN9q7zROILH3MEXdoO6OaRAJO8Ibagtan96fPXiofDJhMWNmjOOJew1\"\n", "    \n", "    def get_vehicle_images(self, bh=\"SV2025072804092\", clggbh=\"\", base_url=\"http://www.jdcsww.com\"):\n", "        \"\"\"获取车辆图片信息（这可能包含车辆数据）\"\"\"\n", "        try:\n", "            # print(f\"正在获取车辆图片信息: {bh}\")\n", "            \n", "            # 设置请求头\n", "            token = self.get_verification_token(base_url)\n", "            \n", "            # 设置cookie\n", "            self.session.cookies.set('__RequestVerificationToken', token)\n", "            self.session.cookies.set('addcontentvisit', '0')\n", "            \n", "            # 设置referer\n", "            self.session.headers['Referer'] = f'{base_url}/qcggdetail?bh={bh}'\n", "            \n", "            # 构建API请求URL\n", "            api_url = f\"{base_url}/getimgs?bh={bh}&clggbh={clggbh}\"\n", "            \n", "            # print(f\"请求URL: {api_url}\")\n", "            \n", "            # 发送请求\n", "            response = self.session.get(api_url, timeout=15)\n", "            \n", "            # print(f\"响应状态码: {response.status_code}\")\n", "            # print(f\"响应头: {dict(response.headers)}\")\n", "            \n", "            if response.status_code == 200:\n", "                try:\n", "                    data = response.json()\n", "                    # print(\"✓ 成功获取JSON数据!\")\n", "                    return data\n", "                except json.JSONDecodeError:\n", "                    # print(\"响应不是JSON格式，返回文本内容\")\n", "                    return {\"text_content\": response.text}\n", "            else:\n", "                # print(f\"请求失败，状态码: {response.status_code}\")\n", "                return {\"error\": f\"HTTP {response.status_code}\", \"content\": response.text}\n", "                \n", "        except Exception as e:\n", "            # print(f\"获取车辆图片信息失败: {e}\")\n", "            return {\"error\": str(e)}\n", "    \n", "    def get_vehicle_detail(self, bh=\"SV2025072804092\", base_url=\"http://www.jdcsww.com\"):\n", "        \"\"\"获取车辆详细信息\"\"\"\n", "        try:\n", "            # print(f\"正在获取车辆详细信息: {bh}\")\n", "            \n", "            # 可能的其他API端点\n", "            api_endpoints = [\n", "                f\"{base_url}/getdetail?bh={bh}\",\n", "                f\"{base_url}/api/vehicle?bh={bh}\",\n", "                f\"{base_url}/qcggdetail/getdata?bh={bh}\",\n", "                f\"{base_url}/vehicle/detail?bh={bh}\"\n", "            ]\n", "            \n", "            for api_url in api_endpoints:\n", "                try:\n", "                    # print(f\"尝试API: {api_url}\")\n", "                    response = self.session.get(api_url, timeout=10)\n", "                    \n", "                    if response.status_code == 200:\n", "                        try:\n", "                            data = response.json()\n", "                            # print(f\"✓ API成功: {api_url}\")\n", "                            return data\n", "                        except:\n", "                            if len(response.text) > 100:  # 有实际内容\n", "                                # print(f\"✓ 获取到文本数据: {api_url}\")\n", "                                return {\"text_content\": response.text}\n", "                    \n", "                except Exception as e:\n", "                    # print(f\"API失败 {api_url}: {e}\")\n", "                    continue\n", "            \n", "            return None\n", "            \n", "        except Exception as e:\n", "            # print(f\"获取车辆详细信息失败: {e}\")\n", "            return {\"error\": str(e)}\n", "    \n", "    def scrape_vehicle_info(self, bh=\"SV2025072804092\", base_url=\"http://www.jdcsww.com\"):\n", "        \"\"\"主要爬取方法\"\"\"\n", "        # print(\"=\" * 60)\n", "        # print(\"车辆信息爬虫 - 成功版本\")\n", "        # print(\"=\" * 60)\n", "        # print(f\"目标车辆编号: {bh}\")\n", "        # print(f\"目标URL: {base_url}/qcggdetail?bh={bh}\")\n", "        # print(f\"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\")\n", "        \n", "        results = {\n", "            \"vehicle_id\": bh,\n", "            \"base_url\": base_url,\n", "            \"scrape_time\": time.strftime('%Y-%m-%d %H:%M:%S'),\n", "            \"status\": \"success\",\n", "            \"data\": {}\n", "        }\n", "        \n", "        # 方法1: 获取图片信息\n", "        # print(\"\\n--- 方法1: 获取车辆图片信息 ---\")\n", "        images_data = self.get_vehicle_images(bh, \"\", base_url)\n", "        if images_data and not images_data.get(\"error\"):\n", "            results[\"data\"][\"images\"] = images_data\n", "            # print(\"✓ 图片信息获取成功\")\n", "        else:\n", "            pass\n", "            # print(\"✗ 图片信息获取失败\")\n", "        \n", "        # 方法2: 尝试其他API端点\n", "        # print(\"\\n--- 方法2: 尝试其他API端点 ---\")\n", "        detail_data = self.get_vehicle_detail(bh, base_url)\n", "        if detail_data and not detail_data.get(\"error\"):\n", "            results[\"data\"][\"details\"] = detail_data\n", "            # print(\"✓ 详细信息获取成功\")\n", "        else:\n", "            pass\n", "            # print(\"✗ 详细信息获取失败\")\n", "        \n", "        # 方法3: 直接访问页面并解析\n", "        # print(\"\\n--- 方法3: 直接页面访问 ---\")\n", "        page_data = self.get_page_content(bh, base_url)\n", "        if page_data:\n", "            results[\"data\"][\"page_content\"] = page_data\n", "            # print(\"✓ 页面内容获取成功\")\n", "        else:\n", "            pass\n", "            # print(\"✗ 页面内容获取失败\")\n", "        \n", "        return results\n", "    \n", "    def get_page_content(self, bh, base_url=\"http://www.jdcsww.com\"):\n", "        \"\"\"获取页面内容\"\"\"\n", "        try:\n", "            url = f\"{base_url}/qcggdetail?bh={bh}\"\n", "            \n", "            # 更新请求头为页面请求\n", "            page_headers = {\n", "                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',\n", "                'Sec-Fetch-Dest': 'document',\n", "                'Sec-Fetch-Mode': 'navigate',\n", "                'Sec-Fetch-Site': 'none',\n", "                'Sec-Fetch-User': '?1',\n", "                'Upgrade-Insecure-Requests': '1'\n", "            }\n", "            \n", "            # 临时更新请求头\n", "            original_headers = self.session.headers.copy()\n", "            self.session.headers.update(page_headers)\n", "            \n", "            response = self.session.get(url, timeout=15)\n", "            \n", "            # 恢复原始请求头\n", "            self.session.headers = original_headers\n", "            \n", "            if response.status_code == 200:\n", "                from bs4 import BeautifulSoup\n", "                soup = BeautifulSoup(response.text, 'html.parser')\n", "                \n", "                # 提取有用信息\n", "                page_info = {\n", "                    \"title\": soup.find('title').get_text() if soup.find('title') else \"\",\n", "                    \"tables\": [],\n", "                    \"scripts\": [],\n", "                    \"forms\": []\n", "                }\n", "                \n", "                # 提取表格\n", "                tables = soup.find_all('table')\n", "                for table in tables:\n", "                    rows = []\n", "                    for row in table.find_all('tr'):\n", "                        cells = [cell.get_text().strip() for cell in row.find_all(['td', 'th'])]\n", "                        if any(cells):\n", "                            rows.append(cells)\n", "                    if rows:\n", "                        page_info[\"tables\"].append(rows)\n", "                \n", "                # 提取脚本中的数据\n", "                scripts = soup.find_all('script')\n", "                for script in scripts:\n", "                    script_text = script.get_text()\n", "                    if 'vehicle' in script_text.lower() or 'data' in script_text.lower():\n", "                        page_info[\"scripts\"].append(script_text[:500])  # 只保存前500字符\n", "                \n", "                return page_info\n", "            \n", "            return None\n", "            \n", "        except Exception as e:\n", "            print(f\"获取页面内容失败: {e}\")\n", "            return None\n", "    \n", "    def save_results(self, data, vehicle_id=None):\n", "        \"\"\"保存结果到分类文件夹\"\"\"\n", "        import os\n", "        \n", "        try:\n", "            # 获取车辆编号\n", "            if not vehicle_id:\n", "                vehicle_id = data.get('vehicle_id', 'unknown')\n", "            \n", "            # 创建文件夹\n", "            json_dir = 'json'\n", "            csv_dir = 'csv'\n", "            os.makedirs(json_dir, exist_ok=True)\n", "            os.makedirs(csv_dir, exist_ok=True)\n", "            \n", "            # 生成文件名\n", "            json_filename = os.path.join(json_dir, f\"vehicle_{vehicle_id}.json\")\n", "            \n", "            # 保存JSON文件\n", "            with open(json_filename, 'w', encoding='utf-8') as f:\n", "                json.dump(data, f, ensure_ascii=False, indent=2)\n", "            print(f\"\\n✓ JSON文件已保存到: {json_filename}\")\n", "            \n", "            # 自动生成CSV文件\n", "            self.auto_generate_csv(data, vehicle_id, csv_dir)\n", "            \n", "            return True\n", "        except Exception as e:\n", "            print(f\"\\n✗ 保存失败: {e}\")\n", "            return False\n", "    \n", "    def auto_generate_csv(self, data, vehicle_id, csv_dir):\n", "        \"\"\"自动生成CSV文件到指定文件夹\"\"\"\n", "        try:\n", "            import pandas as pd\n", "            import os\n", "            \n", "            # 提取关键信息\n", "            vehicle_info = extract_key_vehicle_info(data)\n", "            \n", "            # 生成CSV文件名\n", "            csv_filename = os.path.join(csv_dir, f\"vehicle_{vehicle_id}.csv\")\n", "            \n", "            # 创建DataFrame并保存\n", "            df = pd.DataFrame([vehicle_info])\n", "            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')\n", "            \n", "            # print(f\"✓ CSV文件已保存到: {csv_filename}\")\n", "            # print(f\"  包含 {len(vehicle_info)} 个关键字段\")\n", "            \n", "        except ImportError:\n", "            print(\"⚠️ 无法导入CSV转换模块，请手动运行 python json_to_csv.py\")\n", "        except Exception as e:\n", "            print(f\"⚠️ CSV自动生成失败: {e}\")\n", "\n", "\n", "def scrape_from_url(url):\n", "    \"\"\"从完整URL爬取车辆信息的便捷函数\"\"\"\n", "    import re\n", "    from urllib.parse import urlparse, parse_qs\n", "    \n", "    # 解析URL\n", "    parsed = urlparse(url)\n", "    base_url = f\"{parsed.scheme}://{parsed.netloc}\"\n", "    \n", "    # 提取车辆编号\n", "    query_params = parse_qs(parsed.query)\n", "    bh = query_params.get('bh', [''])[0]\n", "    \n", "    if not bh:\n", "        print(\"❌ 无法从URL中提取车辆编号(bh参数)\")\n", "        return None\n", "    \n", "    print(f\"📋 解析URL:\")\n", "    print(f\"   基础URL: {base_url}\")\n", "    print(f\"   车辆编号: {bh}\")\n", "    print(f\"   完整URL: {url}\")\n", "    \n", "    # 创建爬虫并执行\n", "    scraper = SuccessfulVehicleScraper()\n", "    result = scraper.scrape_vehicle_info(bh, base_url)\n", "    \n", "    # 保存结果\n", "    if result and result.get('status') == 'success':\n", "        scraper.save_results(result, bh)\n", "    \n", "    return result\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import time\n", "import random\n", "import numpy as np\n", "import math\n", "\n", "def batch_scrape_vehicles(vehicle_urls_list, delay_range=(2, 5)):\n", "    \"\"\"\n", "    批量爬取多个车辆信息\n", "    \n", "    Args:\n", "        vehicle_urls: 车辆URL列表\n", "        delay_range: 延迟时间范围(秒)，避免请求过快\n", "    \"\"\"\n", "    import os\n", "    \n", "    # print(\"=\" * 70)\n", "    # print(\"批量车辆信息爬虫\")\n", "    # print(\"=\" * 70)\n", "    print(f\"准备爬取 {len(vehicle_urls_list)} 个车辆信息\")\n", "    \n", "    success_count = 0\n", "    failed_count = 0\n", "    success_vehicles = []\n", "    failed_vehicles = []\n", "    status = []\n", "    for i, url in enumerate(vehicle_urls_list, 1):\n", "\n", "        if not type(url) == str:\n", "            status.append(-1)\n", "            continue\n", "\n", "        if 'http' in url:\n", "            url = url\n", "        else:\n", "            url = f'http://www.jdcsww.com/qcggdetail?bh={url}'\n", "\n", "        print(f\"\\n[{i}/{len(vehicle_urls_list)}] 正在爬取: {url}\")\n", "        \n", "        try:\n", "            result = scrape_from_url(url)\n", "            \n", "            if result and result.get('status') == 'success':\n", "                success_count += 1\n", "                vehicle_id = result.get('vehicle_id', 'unknown')\n", "                success_vehicles.append(vehicle_id)\n", "                print(f\"✓ 第 {i} 个车辆爬取成功: {vehicle_id}\")\n", "                status.append(1)\n", "            else:\n", "                failed_count += 1\n", "                failed_vehicles.append(url)\n", "                print(f\"✗ 第 {i} 个车辆爬取失败: 可能车辆编号不存在\")\n", "                status.append(0)\n", "                \n", "        except Exception as e:\n", "            failed_count += 1\n", "            failed_vehicles.append(url)\n", "            print(f\"✗ 第 {i} 个车辆爬取异常: {str(e)}\")\n", "            status.append(0)\n", "        \n", "        # 添加随机延迟，避免请求过快\n", "        if i < len(vehicle_urls_list):  # 最后一个不需要延迟\n", "            delay = random.uniform(delay_range[0], delay_range[1])\n", "            print(f\"⏳ 等待 {delay:.1f} 秒...\")\n", "            time.sleep(delay)\n", "    \n", "    print(\"\\n\" + \"=\" * 70)\n", "    print(\"批量爬取完成\")\n", "    print(\"=\" * 70)\n", "    print(f\"✓ 成功: {success_count} 个\")\n", "    print(f\"✗ 失败: {failed_count} 个\")\n", "\n", "    \n", "    # 显示成功爬取的车辆\n", "    if success_vehicles:\n", "        print(f\"\\n📋 成功爬取的车辆:\")\n", "        for vehicle_id in success_vehicles:\n", "            print(f\"   - {vehicle_id}\")\n", "    \n", "    # 显示失败的URL\n", "    if failed_vehicles:\n", "        print(f\"\\n❌ 失败的车辆URL:\")\n", "        for url in failed_vehicles:\n", "            print(f\"   - {url}\")\n", "    \n", "    # 列出实际生成的文件\n", "    # print(f\"\\n📁 实际生成的文件:\")\n", "    \n", "    # 检查JSON文件\n", "    # json_dir = './json'\n", "    # if os.path.exists(json_dir):\n", "    #     json_files = [f for f in os.listdir(json_dir) if f.endswith('.json')]\n", "    #     if json_files:\n", "    #         print(f\"   JSON文件 ({len(json_files)} 个):\")\n", "    #         for file in json_files:\n", "    #             print(f\"     - {file}\")\n", "    #     else:\n", "    #         print(f\"   JSON文件: 无\")\n", "    # else:\n", "    #     print(f\"   JSON文件夹不存在\")\n", "    \n", "    # # 检查CSV文件\n", "    # csv_dir = './csv'\n", "    # if os.path.exists(csv_dir):\n", "    #     csv_files = [f for f in os.listdir(csv_dir) if f.endswith('.csv')]\n", "    #     if csv_files:\n", "    #         print(f\"   CSV文件 ({len(csv_files)} 个):\")\n", "    #         for file in csv_files:\n", "    #             print(f\"     - {file}\")\n", "    #     else:\n", "    #         print(f\"   CSV文件: 无\")\n", "    # else:\n", "    #     print(f\"   CSV文件夹不存在\")\n", "\n", "    return status"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "\n", "def get_all_csv_data(folder_path):\n", "    data_list = []\n", "    for root, _, files in os.walk(folder_path):\n", "        for file in files:\n", "            if file.endswith('.csv'):\n", "                # print(root)\n", "                # file_path = os.path.join(root, file)\n", "                file_path = f'{root}/{file}'\n", "                data_list.append(file_path)\n", "    return data_list\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "\n", "def get_all_csv_name(folder_path):\n", "    data_list = []\n", "    for root, _, files in os.walk(folder_path):\n", "        for file in files:\n", "            if file.endswith('.csv'):\n", "                # print(root)\n", "                # file_path = os.path.join(root, file)\n", "                # file_path = f'{root}/{file}'\n", "                data_list.append(file)\n", "    return data_list\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["./veh_update//infos_插电_108.csv\n", "准备爬取 51 个车辆信息\n", "\n", "[1/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025022701376\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025022701376\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025022701376\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025022701376.json\n", "✓ 第 1 个车辆爬取成功: SV2025022701376\n", "⏳ 等待 4.4 秒...\n", "\n", "[2/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025022701321\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025022701321\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025022701321\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025022701321.json\n", "✓ 第 2 个车辆爬取成功: SV2025022701321\n", "⏳ 等待 3.7 秒...\n", "\n", "[3/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025022701420\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025022701420\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025022701420\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025022701420.json\n", "✓ 第 3 个车辆爬取成功: SV2025022701420\n", "⏳ 等待 3.9 秒...\n", "\n", "[4/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025022701305\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025022701305\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025022701305\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025022701305.json\n", "✓ 第 4 个车辆爬取成功: SV2025022701305\n", "⏳ 等待 5.4 秒...\n", "\n", "[5/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025022701110\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025022701110\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025022701110\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025022701110.json\n", "✓ 第 5 个车辆爬取成功: SV2025022701110\n", "⏳ 等待 5.0 秒...\n", "\n", "[6/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025032403565\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025032403565\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025032403565\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025032403565.json\n", "✓ 第 6 个车辆爬取成功: SV2025032403565\n", "⏳ 等待 4.2 秒...\n", "\n", "[7/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025060305919\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025060305919\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025060305919\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025060305919.json\n", "✓ 第 7 个车辆爬取成功: SV2025060305919\n", "⏳ 等待 5.1 秒...\n", "\n", "[8/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025060305866\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025060305866\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025060305866\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025060305866.json\n", "✓ 第 8 个车辆爬取成功: SV2025060305866\n", "⏳ 等待 4.0 秒...\n", "\n", "[9/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025060307010\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025060307010\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025060307010\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025060307010.json\n", "✓ 第 9 个车辆爬取成功: SV2025060307010\n", "⏳ 等待 6.0 秒...\n", "\n", "[10/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025072806269\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025072806269\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025072806269\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025072806269.json\n", "✓ 第 10 个车辆爬取成功: SV2025072806269\n", "⏳ 等待 5.3 秒...\n", "\n", "[11/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025072809042\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025072809042\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025072809042\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025072809042.json\n", "✓ 第 11 个车辆爬取成功: SV2025072809042\n", "⏳ 等待 4.9 秒...\n", "\n", "[12/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025063003100\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025063003100\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025063003100\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025063003100.json\n", "✓ 第 12 个车辆爬取成功: SV2025063003100\n", "⏳ 等待 3.5 秒...\n", "\n", "[13/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025063002470\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025063002470\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025063002470\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025063002470.json\n", "✓ 第 13 个车辆爬取成功: SV2025063002470\n", "⏳ 等待 5.8 秒...\n", "\n", "[14/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025063006425\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025063006425\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025063006425\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025063006425.json\n", "✓ 第 14 个车辆爬取成功: SV2025063006425\n", "⏳ 等待 4.2 秒...\n", "\n", "[15/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025063010685\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025063010685\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025063010685\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025063010685.json\n", "✓ 第 15 个车辆爬取成功: SV2025063010685\n", "⏳ 等待 5.0 秒...\n", "\n", "[16/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022042803805\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022042803805\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022042803805\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022042803805.json\n", "✓ 第 16 个车辆爬取成功: SV2022042803805\n", "⏳ 等待 5.9 秒...\n", "\n", "[17/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2024042202125\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2024042202125\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2024042202125\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2024042202125.json\n", "✓ 第 17 个车辆爬取成功: SV2024042202125\n", "⏳ 等待 5.9 秒...\n", "\n", "[18/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2024052800514\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2024052800514\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2024052800514\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2024052800514.json\n", "✓ 第 18 个车辆爬取成功: SV2024052800514\n", "⏳ 等待 5.5 秒...\n", "\n", "[19/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2021111700894\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2021111700894\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2021111700894\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2021111700894.json\n", "✓ 第 19 个车辆爬取成功: SV2021111700894\n", "⏳ 等待 3.3 秒...\n", "\n", "[20/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2021111707742\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2021111707742\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2021111707742\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2021111707742.json\n", "✓ 第 20 个车辆爬取成功: SV2021111707742\n", "⏳ 等待 5.2 秒...\n", "\n", "[21/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022011010178\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022011010178\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022011010178\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022011010178.json\n", "✓ 第 21 个车辆爬取成功: SV2022011010178\n", "⏳ 等待 6.0 秒...\n", "\n", "[22/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022032912962\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022032912962\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022032912962\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022032912962.json\n", "✓ 第 22 个车辆爬取成功: SV2022032912962\n", "⏳ 等待 5.8 秒...\n", "\n", "[23/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022032913215\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022032913215\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022032913215\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022032913215.json\n", "✓ 第 23 个车辆爬取成功: SV2022032913215\n", "⏳ 等待 5.8 秒...\n", "\n", "[24/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV190617006173\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV190617006173\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV190617006173\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV190617006173.json\n", "✓ 第 24 个车辆爬取成功: SV190617006173\n", "⏳ 等待 4.2 秒...\n", "\n", "[25/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV190617005730\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV190617005730\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV190617005730\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV190617005730.json\n", "✓ 第 25 个车辆爬取成功: SV190617005730\n", "⏳ 等待 5.6 秒...\n", "\n", "[26/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV191022003275\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV191022003275\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV191022003275\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV191022003275.json\n", "✓ 第 26 个车辆爬取成功: SV191022003275\n", "⏳ 等待 5.9 秒...\n", "\n", "[27/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2021122403343\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2021122403343\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2021122403343\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2021122403343.json\n", "✓ 第 27 个车辆爬取成功: SV2021122403343\n", "⏳ 等待 4.8 秒...\n", "\n", "[28/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2021122403311\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2021122403311\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2021122403311\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2021122403311.json\n", "✓ 第 28 个车辆爬取成功: SV2021122403311\n", "⏳ 等待 4.3 秒...\n", "\n", "[29/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2021122409998\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2021122409998\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2021122409998\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2021122409998.json\n", "✓ 第 29 个车辆爬取成功: SV2021122409998\n", "⏳ 等待 3.8 秒...\n", "\n", "[30/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2021122405534\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2021122405534\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2021122405534\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2021122405534.json\n", "✓ 第 30 个车辆爬取成功: SV2021122405534\n", "⏳ 等待 4.7 秒...\n", "\n", "[31/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022011002315\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022011002315\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022011002315\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022011002315.json\n", "✓ 第 31 个车辆爬取成功: SV2022011002315\n", "⏳ 等待 5.4 秒...\n", "\n", "[32/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022011002707\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022011002707\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022011002707\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022011002707.json\n", "✓ 第 32 个车辆爬取成功: SV2022011002707\n", "⏳ 等待 5.2 秒...\n", "\n", "[33/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022011002718\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022011002718\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022011002718\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022011002718.json\n", "✓ 第 33 个车辆爬取成功: SV2022011002718\n", "⏳ 等待 3.8 秒...\n", "\n", "[34/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022011008149\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022011008149\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022011008149\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022011008149.json\n", "✓ 第 34 个车辆爬取成功: SV2022011008149\n", "⏳ 等待 3.4 秒...\n", "\n", "[35/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022011007881\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022011007881\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022011007881\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022011007881.json\n", "✓ 第 35 个车辆爬取成功: SV2022011007881\n", "⏳ 等待 3.7 秒...\n", "\n", "[36/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022011003801\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022011003801\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022011003801\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022011003801.json\n", "✓ 第 36 个车辆爬取成功: SV2022011003801\n", "⏳ 等待 4.0 秒...\n", "\n", "[37/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022011002370\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022011002370\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022011002370\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022011002370.json\n", "✓ 第 37 个车辆爬取成功: SV2022011002370\n", "⏳ 等待 3.4 秒...\n", "\n", "[38/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022032911935\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022032911935\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022032911935\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022032911935.json\n", "✓ 第 38 个车辆爬取成功: SV2022032911935\n", "⏳ 等待 5.1 秒...\n", "\n", "[39/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022032911837\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022032911837\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022032911837\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022032911837.json\n", "✓ 第 39 个车辆爬取成功: SV2022032911837\n", "⏳ 等待 4.5 秒...\n", "\n", "[40/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2022061404768\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2022061404768\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2022061404768\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2022061404768.json\n", "✓ 第 40 个车辆爬取成功: SV2022061404768\n", "⏳ 等待 4.4 秒...\n", "\n", "[41/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2025072803838\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2025072803838\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2025072803838\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2025072803838.json\n", "✓ 第 41 个车辆爬取成功: SV2025072803838\n", "⏳ 等待 5.1 秒...\n", "\n", "[42/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2024052802655\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2024052802655\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2024052802655\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2024052802655.json\n", "✓ 第 42 个车辆爬取成功: SV2024052802655\n", "⏳ 等待 5.3 秒...\n", "\n", "[43/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2024052800393\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2024052800393\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2024052800393\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2024052800393.json\n", "✓ 第 43 个车辆爬取成功: SV2024052800393\n", "⏳ 等待 5.5 秒...\n", "\n", "[44/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2024052800417\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2024052800417\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2024052800417\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2024052800417.json\n", "✓ 第 44 个车辆爬取成功: SV2024052800417\n", "⏳ 等待 5.0 秒...\n", "\n", "[45/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2024032100872\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2024032100872\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2024032100872\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2024032100872.json\n", "✓ 第 45 个车辆爬取成功: SV2024032100872\n", "⏳ 等待 5.6 秒...\n", "\n", "[46/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2024052800122\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2024052800122\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2024052800122\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2024052800122.json\n", "✓ 第 46 个车辆爬取成功: SV2024052800122\n", "⏳ 等待 5.0 秒...\n", "\n", "[47/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2024052800158\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2024052800158\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2024052800158\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2024052800158.json\n", "✓ 第 47 个车辆爬取成功: SV2024052800158\n", "⏳ 等待 4.7 秒...\n", "\n", "[48/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2024052800891\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2024052800891\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2024052800891\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2024052800891.json\n", "✓ 第 48 个车辆爬取成功: SV2024052800891\n", "⏳ 等待 4.5 秒...\n", "\n", "[49/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2024052800802\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2024052800802\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2024052800802\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2024052800802.json\n", "✓ 第 49 个车辆爬取成功: SV2024052800802\n", "⏳ 等待 5.1 秒...\n", "\n", "[50/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2024052801326\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2024052801326\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2024052801326\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2024052801326.json\n", "✓ 第 50 个车辆爬取成功: SV2024052801326\n", "⏳ 等待 3.8 秒...\n", "\n", "[51/51] 正在爬取: http://www.jdcsww.com/qcggdetail?bh=SV2024052801378\n", "📋 解析URL:\n", "   基础URL: http://www.jdcsww.com\n", "   车辆编号: SV2024052801378\n", "   完整URL: http://www.jdcsww.com/qcggdetail?bh=SV2024052801378\n", "\n", "✓ JSON文件已保存到: json\\vehicle_SV2024052801378.json\n", "✓ 第 51 个车辆爬取成功: SV2024052801378\n", "\n", "======================================================================\n", "批量爬取完成\n", "======================================================================\n", "✓ 成功: 51 个\n", "✗ 失败: 0 个\n", "\n", "📋 成功爬取的车辆:\n", "   - SV2025022701376\n", "   - SV2025022701321\n", "   - SV2025022701420\n", "   - SV2025022701305\n", "   - SV2025022701110\n", "   - SV2025032403565\n", "   - SV2025060305919\n", "   - SV2025060305866\n", "   - SV2025060307010\n", "   - SV2025072806269\n", "   - SV2025072809042\n", "   - SV2025063003100\n", "   - SV2025063002470\n", "   - SV2025063006425\n", "   - SV2025063010685\n", "   - SV2022042803805\n", "   - SV2024042202125\n", "   - SV2024052800514\n", "   - SV2021111700894\n", "   - SV2021111707742\n", "   - SV2022011010178\n", "   - SV2022032912962\n", "   - SV2022032913215\n", "   - SV190617006173\n", "   - SV190617005730\n", "   - SV191022003275\n", "   - SV2021122403343\n", "   - SV2021122403311\n", "   - SV2021122409998\n", "   - SV2021122405534\n", "   - SV2022011002315\n", "   - SV2022011002707\n", "   - SV2022011002718\n", "   - SV2022011008149\n", "   - SV2022011007881\n", "   - SV2022011003801\n", "   - SV2022011002370\n", "   - SV2022032911935\n", "   - SV2022032911837\n", "   - SV2022061404768\n", "   - SV2025072803838\n", "   - SV2024052802655\n", "   - SV2024052800393\n", "   - SV2024052800417\n", "   - SV2024032100872\n", "   - SV2024052800122\n", "   - SV2024052800158\n", "   - SV2024052800891\n", "   - SV2024052800802\n", "   - SV2024052801326\n", "   - SV2024052801378\n", "完成： infos_插电_108.csv\n"]}], "source": ["def main():\n", "    \"\"\"主函数 - 批量爬取示例\"\"\"\n", "\n", "    update_dic = \"./veh_update/\"\n", "    data_list = get_all_csv_data(update_dic)\n", "\n", "\n", "    save_dic = './step3_done/'\n", "\n", "    done_list = get_all_csv_name(save_dic)\n", "\n", "    for csv_file in data_list:\n", "\n", "        csv_name = csv_file.split('/')[-1]\n", "\n", "        if csv_name in done_list:\n", "            continue\n", "\n", "        # if not 'test' in csv_name:\n", "        #     continue\n", "\n", "        print(csv_file)\n", "        df_csv = pd.read_csv(csv_file)\n", "\n", "        sv_list = df_csv['sv'].values\n", "\n", "\n", "    \n", "        # 开始批量爬取\n", "        status = batch_scrape_vehicles(sv_list, delay_range=(3, 6))\n", "\n", "        df_csv['status'] = status\n", "        df_csv.to_csv(save_dic + csv_name)\n", "\n", "        print('完成：', csv_name)\n", "\n", "        time.sleep(random.ran<PERSON>t(50,70))\n", "\n", "\n", "main()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}